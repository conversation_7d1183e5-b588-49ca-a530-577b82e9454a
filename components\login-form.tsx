"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Logo } from "@/components/ui/logo"
import { Lock, Mail } from "lucide-react"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface LoginFormProps {
  onLogin: (user: User) => void
}

// Mock users for demonstration
const mockUsers = [
  { id: "1", email: "<EMAIL>", password: "admin123", nom: "Adam SMIDA", role: "ADMIN" as const },
  { id: "2", email: "<EMAIL>", password: "juriste123", nom: "Mehdi CHAARI", role: "JURIDIQUE" as const },
  { id: "3", email: "<EMAIL>", password: "user123", nom: "<PERSON><PERSON>", role: "STANDARD" as const },
]

export function LoginForm({ onLogin }: LoginFormProps) {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    // Simulate API call
    setTimeout(() => {
      const user = mockUsers.find((u) => u.email === email && u.password === password)

      if (user) {
        onLogin({
          id: user.id,
          email: user.email,
          nom: user.nom,
          role: user.role,
        })
      } else {
        setError("Email ou mot de passe incorrect")
      }
      setLoading(false)
    }, 1000)
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <Logo variant="login" />
        </div>
        <CardTitle className="text-2xl">Connexion</CardTitle>
        <CardDescription>Système de Gestion Foncière et Juridique</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="pl-10"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Mot de passe</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pl-10"
                required
              />
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? "Connexion..." : "Se connecter"}
          </Button>
        </form>

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <p className="text-sm font-medium text-gray-700 mb-2">Comptes de démonstration:</p>
          <div className="space-y-1 text-xs text-gray-600">
            <div>👑 Admin: <EMAIL> / admin123</div>
            <div>⚖️ Juriste: <EMAIL> / juriste123</div>
            <div>👤 Utilisateur: <EMAIL> / user123</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
