"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { 
  Bell, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  Search, 
  Filter,
  MarkAsRead,
  Trash2,
  Calendar,
  FileText,
  MapPin,
  User
} from "lucide-react"
import { notificationService, DocumentNotification, NotificationStats } from "@/lib/notification-service"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface NotificationsManagementProps {
  user: User
}

export function NotificationsManagement({ user }: NotificationsManagementProps) {
  const [notifications, setNotifications] = useState<DocumentNotification[]>([])
  const [stats, setStats] = useState<NotificationStats>({ total: 0, unread: 0, expired: 0, expiringSoon: 0, renewalDue: 0 })
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<string>("ALL")
  const [priorityFilter, setPriorityFilter] = useState<string>("ALL")
  const [selectedNotification, setSelectedNotification] = useState<DocumentNotification | null>(null)

  useEffect(() => {
    loadNotifications()
  }, [])

  const loadNotifications = () => {
    const allNotifications = notificationService.getAllNotifications()
    const notificationStats = notificationService.getNotificationStats()
    setNotifications(allNotifications)
    setStats(notificationStats)
  }

  const filteredNotifications = notifications.filter((notification) => {
    const matchesSearch = 
      notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.documentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (notification.parcelNumber && notification.parcelNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (notification.owner && notification.owner.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesType = typeFilter === "ALL" || notification.type === typeFilter
    const matchesPriority = priorityFilter === "ALL" || notification.priority === priorityFilter
    
    return matchesSearch && matchesType && matchesPriority
  })

  const handleMarkAsRead = (notificationId: string) => {
    notificationService.markAsRead(notificationId)
    loadNotifications()
  }

  const handleMarkAllAsRead = () => {
    notificationService.markAllAsRead()
    loadNotifications()
  }

  const handleDeleteNotification = (notificationId: string) => {
    notificationService.deleteNotification(notificationId)
    loadNotifications()
  }

  const handleViewDetails = (notification: DocumentNotification) => {
    setSelectedNotification(notification)
    if (!notification.isRead) {
      handleMarkAsRead(notification.id)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "EXPIRED":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case "EXPIRING":
        return <Clock className="h-4 w-4 text-orange-500" />
      case "RENEWAL_DUE":
        return <Calendar className="h-4 w-4 text-blue-500" />
      default:
        return <Bell className="h-4 w-4 text-gray-500" />
    }
  }

  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case "EXPIRED":
        return "destructive"
      case "EXPIRING":
        return "secondary"
      case "RENEWAL_DUE":
        return "outline"
      default:
        return "secondary"
    }
  }

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case "HIGH":
        return "destructive"
      case "MEDIUM":
        return "secondary"
      case "LOW":
        return "outline"
      default:
        return "secondary"
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "EXPIRED":
        return "Expiré"
      case "EXPIRING":
        return "Expire Bientôt"
      case "RENEWAL_DUE":
        return "Renouvellement"
      default:
        return type
    }
  }

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case "HIGH":
        return "Haute"
      case "MEDIUM":
        return "Moyenne"
      case "LOW":
        return "Basse"
      default:
        return priority
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return "Il y a moins d'1h"
    if (diffInHours < 24) return `Il y a ${diffInHours}h`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `Il y a ${diffInDays} jour(s)`
    
    return date.toLocaleDateString('fr-FR')
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">notifications</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Non Lues</CardTitle>
            <MarkAsRead className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.unread}</div>
            <p className="text-xs text-muted-foreground">à traiter</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expirés</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.expired}</div>
            <p className="text-xs text-muted-foreground">urgents</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expire Bientôt</CardTitle>
            <Clock className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.expiringSoon}</div>
            <p className="text-xs text-muted-foreground">à surveiller</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Renouvellements</CardTitle>
            <Calendar className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.renewalDue}</div>
            <p className="text-xs text-muted-foreground">à planifier</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Notifications Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Notifications d'Expiration</span>
            </div>
            <div className="flex space-x-2">
              {stats.unread > 0 && (
                <Button variant="outline" size="sm" onClick={handleMarkAllAsRead}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Tout marquer comme lu
                </Button>
              )}
              <Badge variant="outline">
                {filteredNotifications.length} notification(s) affichée(s)
              </Badge>
            </div>
          </CardTitle>
          <CardDescription>
            Alertes automatiques pour les documents arrivant à expiration
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Rechercher par titre, document ou parcelle..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Tous les types</SelectItem>
                <SelectItem value="EXPIRED">Expirés</SelectItem>
                <SelectItem value="EXPIRING">Expire Bientôt</SelectItem>
                <SelectItem value="RENEWAL_DUE">Renouvellement</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par priorité" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Toutes priorités</SelectItem>
                <SelectItem value="HIGH">Haute</SelectItem>
                <SelectItem value="MEDIUM">Moyenne</SelectItem>
                <SelectItem value="LOW">Basse</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Notifications List */}
          <div className="space-y-3">
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  notification.isRead ? "bg-gray-50" : "bg-white border-blue-200"
                } hover:bg-gray-100`}
                onClick={() => handleViewDetails(notification)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="mt-1">
                      {getTypeIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className={`font-medium ${!notification.isRead ? "font-semibold" : ""}`}>
                          {notification.title}
                        </h4>
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{notification.message}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <FileText className="h-3 w-3" />
                          <span>{notification.documentName}</span>
                        </div>
                        {notification.parcelNumber && (
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span>{notification.parcelNumber}</span>
                          </div>
                        )}
                        {notification.owner && (
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{notification.owner}</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{formatTimeAgo(notification.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Badge variant={getTypeBadgeVariant(notification.type)}>
                      {getTypeLabel(notification.type)}
                    </Badge>
                    <Badge variant={getPriorityBadgeVariant(notification.priority)}>
                      {getPriorityLabel(notification.priority)}
                    </Badge>
                    <div className="flex space-x-1">
                      {!notification.isRead && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleMarkAsRead(notification.id)
                          }}
                        >
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteNotification(notification.id)
                        }}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredNotifications.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Bell className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Aucune notification trouvée avec les critères de recherche</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Notification Details Dialog */}
      <Dialog open={!!selectedNotification} onOpenChange={() => setSelectedNotification(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              {selectedNotification && getTypeIcon(selectedNotification.type)}
              <span>Détails de la Notification</span>
            </DialogTitle>
            <DialogDescription>Informations complètes sur l'expiration du document</DialogDescription>
          </DialogHeader>
          {selectedNotification && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-500 mb-1">Type et Priorité</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Badge variant={getTypeBadgeVariant(selectedNotification.type)}>
                          {getTypeLabel(selectedNotification.type)}
                        </Badge>
                        <Badge variant={getPriorityBadgeVariant(selectedNotification.priority)}>
                          Priorité {getPriorityLabel(selectedNotification.priority)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-500 mb-1">Document</h4>
                    <div className="space-y-2">
                      <p><strong>Nom:</strong> {selectedNotification.documentName}</p>
                      <p><strong>Type:</strong> {selectedNotification.documentType}</p>
                      <p><strong>ID:</strong> {selectedNotification.documentId}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-500 mb-1">Expiration</h4>
                    <div className="space-y-2">
                      <p><strong>Date d'expiration:</strong> {new Date(selectedNotification.expirationDate).toLocaleDateString('fr-FR')}</p>
                      <p><strong>Jours restants:</strong>
                        <span className={`ml-1 font-medium ${
                          selectedNotification.daysRemaining < 0 ? 'text-red-600' :
                          selectedNotification.daysRemaining <= 7 ? 'text-orange-600' :
                          'text-blue-600'
                        }`}>
                          {selectedNotification.daysRemaining < 0
                            ? `Expiré depuis ${Math.abs(selectedNotification.daysRemaining)} jour(s)`
                            : `${selectedNotification.daysRemaining} jour(s)`
                          }
                        </span>
                      </p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-500 mb-1">Parcelle et Propriétaire</h4>
                    <div className="space-y-2">
                      {selectedNotification.parcelNumber && (
                        <p><strong>Parcelle:</strong> {selectedNotification.parcelNumber}</p>
                      )}
                      {selectedNotification.owner && (
                        <p><strong>Propriétaire:</strong> {selectedNotification.owner}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-500 mb-1">Message</h4>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700">{selectedNotification.message}</p>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-500 mb-1">Informations de notification</h4>
                <div className="text-sm text-gray-600">
                  <p><strong>Créée le:</strong> {new Date(selectedNotification.createdAt).toLocaleString('fr-FR')}</p>
                  <p><strong>Statut:</strong> {selectedNotification.isRead ? "Lue" : "Non lue"}</p>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                {!selectedNotification.isRead && (
                  <Button
                    variant="outline"
                    onClick={() => handleMarkAsRead(selectedNotification.id)}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Marquer comme lu
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={() => handleDeleteNotification(selectedNotification.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Supprimer
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
