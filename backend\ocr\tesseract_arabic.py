# -*- coding: utf-8 -*-
"""Copy of Welcome to <PERSON>b

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1c3jGjJg4Ex5z7f3AGe5hUJHvSAsR3wqx
"""

# Google Colab Arabic OCR Workflow (Fixed Version)
# ------------------------------------------------

# 1. Install Dependencies
# ---------------------------------
!sudo apt-get install tesseract-ocr-ara -y  # Arabic language pack
!pip install pytesseract Pillow

# 2. Setup Environment
# ---------------------------------
import pytesseract
from PIL import Image
import os
import time
from IPython.display import display, Markdown, clear_output
from google.colab import files

# Configure Tesseract path
pytesseract.pytesseract.tesseract_cmd = '/usr/bin/tesseract'

# 3. File Upload & Processing
# ---------------------------------
def ocr_workflow():
    IMAGE_PATH = "/content/3.png"
    OUTPUT_PATH = "/content/sample_data/extracted_text.txt"

    # Step 1: Upload image
    display(Markdown("**Step 1: Upload your image**"))
    display(Markdown("""
    1. Click the 📁 folder icon on the left
    2. Navigate to `sample_data` folder
    3. Click the 📤 upload button
    4. Select your `1.png` file
    """))

    # Wait for file upload
    input("Press Enter AFTER uploading the image...")
    clear_output()

    # Verify upload
    if not os.path.exists(IMAGE_PATH):
        display(Markdown("**Error:** File not found. Ensure you:"))
        display(Markdown("1. Named it EXACTLY `1.png` (case-sensitive)"))
        display(Markdown("2. Placed in `sample_data` folder"))
        return

    # Step 2: Process image
    try:
        img = Image.open(IMAGE_PATH)
        config = r'--oem 3 --psm 6 -l ara'
        extracted_text = pytesseract.image_to_string(img, config=config)

        # Save and display results
        with open(OUTPUT_PATH, "w", encoding="utf-8") as f:
            f.write(extracted_text.strip())

        display(Markdown("**Extracted Text:**"))
        print(extracted_text)
        display(Markdown(f"**Saved to:** `{OUTPUT_PATH}`"))

    except Exception as e:
        display(Markdown("**Processing Error:**"))
        print(str(e))

# Run the workflow
ocr_workflow()