"use client"

import { useState, useEffect } from "react"
import dynamic from "next/dynamic"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { MapPin, Search, Layers, ZoomIn, ZoomOut, RotateCcw, Zap, Droplets } from "lucide-react"

// Dynamic import to avoid SSR issues with Leaflet
const MapContainer = dynamic(() => import("react-leaflet").then((mod) => mod.MapContainer), { ssr: false })
const TileLayer = dynamic(() => import("react-leaflet").then((mod) => mod.TileLayer), { ssr: false })
const Marker = dynamic(() => import("react-leaflet").then((mod) => mod.Marker), { ssr: false })
const Popup = dynamic(() => import("react-leaflet").then((mod) => mod.Popup), { ssr: false })
const Polygon = dynamic(() => import("react-leaflet").then((mod) => mod.Polygon), { ssr: false })
const Polyline = dynamic(() => import("react-leaflet").then((mod) => mod.Polyline), { ssr: false })
const Circle = dynamic(() => import("react-leaflet").then((mod) => mod.Circle), { ssr: false })

interface Parcel {
  id: string
  numero: string
  coordinates: [number, number] // Center point
  polygon: [number, number][] // Polygon coordinates
  proprietaire: string
  superficie: number
  statut: "Actif" | "Expiré" | "En cours"
  type: "Exploitation" | "Exploration" | "Pipeline" | "Installation"
  dateAcquisition: string
  documents: {
    id: string
    nom: string
    type: string
    dateUpload: string
    statut: string
    taille: string
  }[]
}

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface Pipeline {
  id: string
  nom: string
  coordinates: [number, number][] // Pipeline path
  type: "Transport" | "Distribution" | "Collecte"
  statut: "Actif" | "Maintenance" | "Hors service"
  diametre: number // in mm
  longueur: number // in km
  capacite: number // in barils/jour
  dateConstruction: string
  operateur: string
}

interface Well {
  id: string
  nom: string
  coordinates: [number, number]
  type: "Production" | "Exploration" | "Injection" | "Observation"
  statut: "Actif" | "Fermé" | "En forage" | "Suspendu"
  profondeur: number // in meters
  production: number // in barils/jour
  dateForage: string
  operateur: string
  reservoir: string
}

interface InteractiveMapProps {
  user: User
}

// Southern Tunisia parcels data - Real coordinates around Tataouine/Medenine region
const parcelsData: Parcel[] = [
  {
    id: "P-2024-001",
    numero: "TF-123456",
    coordinates: [32.9297, 10.4518], // Tataouine region
    polygon: [
      [32.9297, 10.4518],
      [32.9350, 10.4580],
      [32.9280, 10.4620],
      [32.9220, 10.4550],
      [32.9297, 10.4518]
    ],
    proprietaire: "Ahmed Benali",
    superficie: 2500,
    statut: "Actif",
    type: "Exploitation",
    dateAcquisition: "2024-01-15",
    documents: [
      { id: "D1", nom: "Contrat_Exploitation_2024.pdf", type: "Contrat", dateUpload: "2024-01-15", statut: "Validé", taille: "2.3 MB" },
      { id: "D2", nom: "Etude_Geologique.pdf", type: "Étude", dateUpload: "2024-01-20", statut: "En cours", taille: "5.1 MB" }
    ]
  },
  {
    id: "P-2024-002",
    numero: "TF-789012",
    coordinates: [32.8845, 10.3912], // Near Remada
    polygon: [
      [32.8845, 10.3912],
      [32.8900, 10.3980],
      [32.8820, 10.4020],
      [32.8780, 10.3950],
      [32.8845, 10.3912]
    ],
    proprietaire: "Fatima Alaoui",
    superficie: 3200,
    statut: "Actif",
    type: "Pipeline",
    dateAcquisition: "2024-02-10",
    documents: [
      { id: "D3", nom: "Autorisation_Pipeline.pdf", type: "Autorisation", dateUpload: "2024-02-10", statut: "Validé", taille: "1.8 MB" },
      { id: "D4", nom: "Plan_Installation.dwg", type: "Plan", dateUpload: "2024-02-15", statut: "Validé", taille: "3.2 MB" }
    ]
  },
  {
    id: "P-2024-003",
    numero: "TF-345678",
    coordinates: [32.7234, 10.7891], // Near Medenine
    polygon: [
      [32.7234, 10.7891],
      [32.7290, 10.7950],
      [32.7210, 10.7990],
      [32.7180, 10.7920],
      [32.7234, 10.7891]
    ],
    proprietaire: "Omar Tazi",
    superficie: 1800,
    statut: "En cours",
    type: "Exploration",
    dateAcquisition: "2024-03-05",
    documents: [
      { id: "D5", nom: "Permis_Exploration.pdf", type: "Permis", dateUpload: "2024-03-05", statut: "En cours", taille: "2.1 MB" }
    ]
  },
  {
    id: "P-2024-004",
    numero: "TF-456789",
    coordinates: [32.6123, 10.5678], // South of Medenine
    polygon: [
      [32.6123, 10.5678],
      [32.6180, 10.5740],
      [32.6100, 10.5780],
      [32.6070, 10.5710],
      [32.6123, 10.5678]
    ],
    proprietaire: "Youssef Bennani",
    superficie: 4100,
    statut: "Actif",
    type: "Installation",
    dateAcquisition: "2023-12-20",
    documents: [
      { id: "D6", nom: "Contrat_Installation.pdf", type: "Contrat", dateUpload: "2023-12-20", statut: "Validé", taille: "2.8 MB" },
      { id: "D7", nom: "Certificat_Conformite.pdf", type: "Certificat", dateUpload: "2024-01-10", statut: "Validé", taille: "1.5 MB" }
    ]
  },
  {
    id: "P-2024-005",
    numero: "TF-567890",
    coordinates: [32.5456, 10.4321], // Desert region
    polygon: [
      [32.5456, 10.4321],
      [32.5520, 10.4390],
      [32.5440, 10.4430],
      [32.5400, 10.4360],
      [32.5456, 10.4321]
    ],
    proprietaire: "Aicha Mansouri",
    superficie: 5600,
    statut: "Expiré",
    type: "Exploration",
    dateAcquisition: "2023-06-15",
    documents: [
      { id: "D8", nom: "Rapport_Final_Exploration.pdf", type: "Rapport", dateUpload: "2023-12-01", statut: "Archivé", taille: "8.7 MB" }
    ]
  }
]

// Pipelines data - Realistic pipeline network in southern Tunisia
const pipelinesData: Pipeline[] = [
  {
    id: "PL-001",
    nom: "Pipeline Principal Tataouine-Gabès",
    coordinates: [
      [32.9297, 10.4518], // Tataouine
      [32.8845, 10.3912], // Remada
      [32.7234, 10.7891], // Medenine
      [33.8869, 10.0982]  // Gabès
    ],
    type: "Transport",
    statut: "Actif",
    diametre: 610, // 24 inches
    longueur: 185,
    capacite: 150000,
    dateConstruction: "2019-03-15",
    operateur: "Entreprise Tunisienne d'Activités Pétrolières (ETAP)"
  },
  {
    id: "PL-002",
    nom: "Pipeline Collecte Sud",
    coordinates: [
      [32.6123, 10.5678], // Well field
      [32.5456, 10.4321], // Collection point
      [32.7234, 10.7891]  // Main pipeline connection
    ],
    type: "Collecte",
    statut: "Actif",
    diametre: 305, // 12 inches
    longueur: 45,
    capacite: 25000,
    dateConstruction: "2021-08-20",
    operateur: "ETAP"
  },
  {
    id: "PL-003",
    nom: "Pipeline Distribution Locale",
    coordinates: [
      [32.7234, 10.7891], // Medenine hub
      [32.6800, 10.8200], // Local distribution
      [32.6500, 10.8500], // End point
    ],
    type: "Distribution",
    statut: "Maintenance",
    diametre: 152, // 6 inches
    longueur: 28,
    capacite: 8000,
    dateConstruction: "2020-11-10",
    operateur: "Société Tunisienne de l'Électricité et du Gaz (STEG)"
  }
]

// Wells data - Oil and gas wells in southern Tunisia
const wellsData: Well[] = [
  {
    id: "W-001",
    nom: "Puits Tataouine-1",
    coordinates: [32.9297, 10.4518],
    type: "Production",
    statut: "Actif",
    profondeur: 3250,
    production: 850,
    dateForage: "2018-05-12",
    operateur: "ETAP",
    reservoir: "Formation Jurassique"
  },
  {
    id: "W-002",
    nom: "Puits Remada-3",
    coordinates: [32.8845, 10.3912],
    type: "Production",
    statut: "Actif",
    profondeur: 2890,
    production: 620,
    dateForage: "2019-09-08",
    operateur: "ETAP",
    reservoir: "Formation Crétacé"
  },
  {
    id: "W-003",
    nom: "Puits Exploration Sud-7",
    coordinates: [32.5456, 10.4321],
    type: "Exploration",
    statut: "En forage",
    profondeur: 4100,
    production: 0,
    dateForage: "2024-01-15",
    operateur: "OMV Tunisia",
    reservoir: "Formation Triasique"
  },
  {
    id: "W-004",
    nom: "Puits Medenine-2",
    coordinates: [32.7234, 10.7891],
    type: "Production",
    statut: "Actif",
    profondeur: 2650,
    production: 420,
    dateForage: "2020-03-22",
    operateur: "ETAP",
    reservoir: "Formation Jurassique"
  },
  {
    id: "W-005",
    nom: "Puits Injection-1",
    coordinates: [32.6123, 10.5678],
    type: "Injection",
    statut: "Actif",
    profondeur: 3100,
    production: 0,
    dateForage: "2021-07-18",
    operateur: "ETAP",
    reservoir: "Formation Crétacé"
  },
  {
    id: "W-006",
    nom: "Puits Observation-4",
    coordinates: [32.8200, 10.6000],
    type: "Observation",
    statut: "Actif",
    profondeur: 1850,
    production: 0,
    dateForage: "2022-11-05",
    operateur: "ETAP",
    reservoir: "Aquifère superficiel"
  }
]

export function InteractiveMap({ user }: InteractiveMapProps) {
  const [selectedParcel, setSelectedParcel] = useState<Parcel | null>(null)
  const [selectedPipeline, setSelectedPipeline] = useState<Pipeline | null>(null)
  const [selectedWell, setSelectedWell] = useState<Well | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [mapCenter, setMapCenter] = useState<[number, number]>([32.7500, 10.5000]) // Center of southern Tunisia
  const [mapZoom, setMapZoom] = useState(9)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showPipelines, setShowPipelines] = useState(true)
  const [showWells, setShowWells] = useState(true)

  const filteredParcels = parcelsData.filter((parcel) =>
    parcel.numero.toLowerCase().includes(searchTerm.toLowerCase()) ||
    parcel.proprietaire.toLowerCase().includes(searchTerm.toLowerCase()) ||
    parcel.type.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getParcelColor = (parcel: Parcel) => {
    switch (parcel.statut) {
      case "Actif":
        return "#22c55e" // Green
      case "En cours":
        return "#f59e0b" // Orange
      case "Expiré":
        return "#ef4444" // Red
      default:
        return "#6b7280" // Gray
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Exploitation":
        return "#3b82f6" // Blue
      case "Pipeline":
        return "#8b5cf6" // Purple
      case "Exploration":
        return "#f59e0b" // Orange
      case "Installation":
        return "#10b981" // Emerald
      default:
        return "#6b7280" // Gray
    }
  }

  const getPipelineColor = (pipeline: Pipeline) => {
    switch (pipeline.statut) {
      case "Actif":
        return "#22c55e" // Green
      case "Maintenance":
        return "#f59e0b" // Orange
      case "Hors service":
        return "#ef4444" // Red
      default:
        return "#6b7280" // Gray
    }
  }

  const getWellColor = (well: Well) => {
    switch (well.type) {
      case "Production":
        return "#22c55e" // Green
      case "Exploration":
        return "#3b82f6" // Blue
      case "Injection":
        return "#8b5cf6" // Purple
      case "Observation":
        return "#f59e0b" // Orange
      default:
        return "#6b7280" // Gray
    }
  }

  const getWellStatusColor = (statut: string) => {
    switch (statut) {
      case "Actif":
        return "#22c55e" // Green
      case "En forage":
        return "#3b82f6" // Blue
      case "Fermé":
        return "#ef4444" // Red
      case "Suspendu":
        return "#f59e0b" // Orange
      default:
        return "#6b7280" // Gray
    }
  }

  const handleParcelClick = (parcel: Parcel) => {
    setSelectedParcel(parcel)
    setMapCenter(parcel.coordinates)
    setMapZoom(12)
  }

  const resetMapView = () => {
    setMapCenter([32.7500, 10.5000])
    setMapZoom(9)
    setSelectedParcel(null)
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  return (
    <div className="space-y-6">
      {/* Map Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Carte Interactive - Sud Tunisie</span>
            </div>
            <div className="flex space-x-2">
              <Button
                variant={showPipelines ? "default" : "outline"}
                size="sm"
                onClick={() => setShowPipelines(!showPipelines)}
              >
                <Zap className="h-4 w-4 mr-2" />
                Pipelines
              </Button>
              <Button
                variant={showWells ? "default" : "outline"}
                size="sm"
                onClick={() => setShowWells(!showWells)}
              >
                <Droplets className="h-4 w-4 mr-2" />
                Puits
              </Button>
              <Button variant="outline" size="sm" onClick={resetMapView}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Réinitialiser
              </Button>
              <Button variant="outline" size="sm" onClick={toggleFullscreen}>
                <Layers className="h-4 w-4 mr-2" />
                {isFullscreen ? "Vue normale" : "Plein écran"}
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Région de Tataouine, Medenine et zones désertiques - {filteredParcels.length} parcelle(s) affichée(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Rechercher par numéro, propriétaire ou type..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Map Container */}
          <div className={`relative ${isFullscreen ? "fixed inset-0 z-50 bg-white" : "h-96"} rounded-lg overflow-hidden border`}>
            {typeof window !== "undefined" && (
              <MapContainer
                center={mapCenter}
                zoom={mapZoom}
                style={{ height: "100%", width: "100%" }}
                className="z-10"
              >
                <TileLayer
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />
                
                {/* Render parcels */}
                {filteredParcels.map((parcel) => (
                  <div key={parcel.id}>
                    {/* Parcel polygon */}
                    <Polygon
                      positions={parcel.polygon}
                      pathOptions={{
                        color: getParcelColor(parcel),
                        fillColor: getParcelColor(parcel),
                        fillOpacity: 0.3,
                        weight: 2
                      }}
                      eventHandlers={{
                        click: () => handleParcelClick(parcel)
                      }}
                    />
                    
                    {/* Parcel marker */}
                    <Marker position={parcel.coordinates}>
                      <Popup>
                        <div className="p-2 min-w-64">
                          <h3 className="font-semibold text-lg mb-2">{parcel.numero}</h3>
                          <div className="space-y-1 text-sm">
                            <p><strong>Propriétaire:</strong> {parcel.proprietaire}</p>
                            <p><strong>Type:</strong> 
                              <Badge 
                                variant="outline" 
                                className="ml-2"
                                style={{ borderColor: getTypeColor(parcel.type), color: getTypeColor(parcel.type) }}
                              >
                                {parcel.type}
                              </Badge>
                            </p>
                            <p><strong>Superficie:</strong> {parcel.superficie.toLocaleString()} m²</p>
                            <p><strong>Statut:</strong> 
                              <Badge 
                                variant={parcel.statut === "Actif" ? "default" : parcel.statut === "En cours" ? "secondary" : "destructive"}
                                className="ml-2"
                              >
                                {parcel.statut}
                              </Badge>
                            </p>
                            <p><strong>Date acquisition:</strong> {parcel.dateAcquisition}</p>
                            <p><strong>Documents:</strong> {parcel.documents.length} fichier(s)</p>
                          </div>
                          <Button 
                            size="sm" 
                            className="mt-3 w-full"
                            onClick={() => setSelectedParcel(parcel)}
                          >
                            Voir détails
                          </Button>
                        </div>
                      </Popup>
                    </Marker>
                  </div>
                ))}

                {/* Render pipelines */}
                {showPipelines && pipelinesData.map((pipeline) => (
                  <Polyline
                    key={pipeline.id}
                    positions={pipeline.coordinates}
                    pathOptions={{
                      color: getPipelineColor(pipeline),
                      weight: pipeline.type === "Transport" ? 6 : pipeline.type === "Collecte" ? 4 : 3,
                      opacity: 0.8
                    }}
                    eventHandlers={{
                      click: () => setSelectedPipeline(pipeline)
                    }}
                  >
                    <Popup>
                      <div className="p-2 min-w-64">
                        <h3 className="font-semibold text-lg mb-2">{pipeline.nom}</h3>
                        <div className="space-y-1 text-sm">
                          <p><strong>Type:</strong> {pipeline.type}</p>
                          <p><strong>Statut:</strong>
                            <Badge
                              variant={pipeline.statut === "Actif" ? "default" : pipeline.statut === "Maintenance" ? "secondary" : "destructive"}
                              className="ml-2"
                            >
                              {pipeline.statut}
                            </Badge>
                          </p>
                          <p><strong>Diamètre:</strong> {pipeline.diametre} mm</p>
                          <p><strong>Longueur:</strong> {pipeline.longueur} km</p>
                          <p><strong>Capacité:</strong> {pipeline.capacite.toLocaleString()} barils/jour</p>
                          <p><strong>Opérateur:</strong> {pipeline.operateur}</p>
                        </div>
                      </div>
                    </Popup>
                  </Polyline>
                ))}

                {/* Render wells */}
                {showWells && wellsData.map((well) => (
                  <Circle
                    key={well.id}
                    center={well.coordinates}
                    radius={well.type === "Production" ? 800 : well.type === "Exploration" ? 600 : 400}
                    pathOptions={{
                      color: getWellColor(well),
                      fillColor: getWellStatusColor(well.statut),
                      fillOpacity: 0.6,
                      weight: 3
                    }}
                    eventHandlers={{
                      click: () => setSelectedWell(well)
                    }}
                  >
                    <Popup>
                      <div className="p-2 min-w-64">
                        <h3 className="font-semibold text-lg mb-2">{well.nom}</h3>
                        <div className="space-y-1 text-sm">
                          <p><strong>Type:</strong> {well.type}</p>
                          <p><strong>Statut:</strong>
                            <Badge
                              variant={well.statut === "Actif" ? "default" : well.statut === "En forage" ? "secondary" : "destructive"}
                              className="ml-2"
                            >
                              {well.statut}
                            </Badge>
                          </p>
                          <p><strong>Profondeur:</strong> {well.profondeur.toLocaleString()} m</p>
                          {well.production > 0 && (
                            <p><strong>Production:</strong> {well.production.toLocaleString()} barils/jour</p>
                          )}
                          <p><strong>Réservoir:</strong> {well.reservoir}</p>
                          <p><strong>Opérateur:</strong> {well.operateur}</p>
                        </div>
                      </div>
                    </Popup>
                  </Circle>
                ))}
              </MapContainer>
            )}
            
            {isFullscreen && (
              <Button
                variant="outline"
                size="sm"
                className="absolute top-4 right-4 z-20"
                onClick={toggleFullscreen}
              >
                Fermer
              </Button>
            )}
          </div>

          {/* Legend */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-3">Légende</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <h5 className="text-sm font-medium mb-2">Statut des parcelles:</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-green-500 rounded"></div>
                    <span>Actif</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-orange-500 rounded"></div>
                    <span>En cours</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-red-500 rounded"></div>
                    <span>Expiré</span>
                  </div>
                </div>
              </div>
              <div>
                <h5 className="text-sm font-medium mb-2">Types d'activité:</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-blue-500 rounded"></div>
                    <span>Exploitation</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-purple-500 rounded"></div>
                    <span>Pipeline</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-orange-500 rounded"></div>
                    <span>Exploration</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-emerald-500 rounded"></div>
                    <span>Installation</span>
                  </div>
                </div>
              </div>
              <div>
                <h5 className="text-sm font-medium mb-2">Pipelines:</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-1 bg-green-500"></div>
                    <span>Actif</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-1 bg-orange-500"></div>
                    <span>Maintenance</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-1 bg-red-500"></div>
                    <span>Hors service</span>
                  </div>
                </div>
              </div>
              <div>
                <h5 className="text-sm font-medium mb-2">Puits:</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                    <span>Production</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                    <span>Exploration</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
                    <span>Injection</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                    <span>Observation</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Parcels List */}
      <Card>
        <CardHeader>
          <CardTitle>Liste des Parcelles</CardTitle>
          <CardDescription>{filteredParcels.length} parcelle(s) dans la région</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredParcels.map((parcel) => (
              <div
                key={parcel.id}
                className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => handleParcelClick(parcel)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{parcel.numero}</h4>
                  <Badge variant={parcel.statut === "Actif" ? "default" : parcel.statut === "En cours" ? "secondary" : "destructive"}>
                    {parcel.statut}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-1">
                  <MapPin className="h-3 w-3 inline mr-1" />
                  {parcel.coordinates[0].toFixed(4)}, {parcel.coordinates[1].toFixed(4)}
                </p>
                <p className="text-sm text-gray-600 mb-1">Propriétaire: {parcel.proprietaire}</p>
                <p className="text-sm text-gray-600 mb-2">
                  Type:
                  <Badge
                    variant="outline"
                    className="ml-1 text-xs"
                    style={{ borderColor: getTypeColor(parcel.type), color: getTypeColor(parcel.type) }}
                  >
                    {parcel.type}
                  </Badge>
                </p>
                <p className="text-sm text-gray-600 mb-2">Superficie: {parcel.superficie.toLocaleString()} m²</p>
                <p className="text-xs text-gray-500">{parcel.documents.length} document(s) associé(s)</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Parcel Details Dialog */}
      <Dialog open={!!selectedParcel} onOpenChange={() => setSelectedParcel(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Détails de la Parcelle {selectedParcel?.numero}</span>
            </DialogTitle>
            <DialogDescription>
              Informations complètes et documents associés à cette parcelle
            </DialogDescription>
          </DialogHeader>
          {selectedParcel && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-500 mb-1">Informations générales</h4>
                    <div className="space-y-2">
                      <p><strong>Numéro:</strong> {selectedParcel.numero}</p>
                      <p><strong>Propriétaire:</strong> {selectedParcel.proprietaire}</p>
                      <p><strong>Superficie:</strong> {selectedParcel.superficie.toLocaleString()} m²</p>
                      <p><strong>Date d'acquisition:</strong> {selectedParcel.dateAcquisition}</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-500 mb-1">Localisation</h4>
                    <div className="space-y-2">
                      <p><strong>Coordonnées:</strong> {selectedParcel.coordinates[0].toFixed(6)}, {selectedParcel.coordinates[1].toFixed(6)}</p>
                      <p><strong>Région:</strong> Sud Tunisie</p>
                      <p><strong>Zone:</strong> {
                        selectedParcel.coordinates[0] > 32.8 ? "Tataouine" :
                        selectedParcel.coordinates[1] > 10.6 ? "Medenine" : "Zone désertique"
                      }</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-500 mb-1">Statut et type</h4>
                    <div className="space-y-2">
                      <p><strong>Statut:</strong>
                        <Badge
                          variant={selectedParcel.statut === "Actif" ? "default" : selectedParcel.statut === "En cours" ? "secondary" : "destructive"}
                          className="ml-2"
                        >
                          {selectedParcel.statut}
                        </Badge>
                      </p>
                      <p><strong>Type d'activité:</strong>
                        <Badge
                          variant="outline"
                          className="ml-2"
                          style={{ borderColor: getTypeColor(selectedParcel.type), color: getTypeColor(selectedParcel.type) }}
                        >
                          {selectedParcel.type}
                        </Badge>
                      </p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-500 mb-1">Documents ({selectedParcel.documents.length})</h4>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {selectedParcel.documents.map((doc) => (
                        <div key={doc.id} className="p-2 bg-gray-50 rounded text-sm">
                          <p className="font-medium">{doc.nom}</p>
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>{doc.type} • {doc.taille}</span>
                            <Badge variant={doc.statut === "Validé" ? "default" : "secondary"} className="text-xs">
                              {doc.statut}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Mini map in dialog */}
              <div className="border rounded-lg overflow-hidden">
                <div className="h-64">
                  {typeof window !== "undefined" && (
                    <MapContainer
                      center={selectedParcel.coordinates}
                      zoom={13}
                      style={{ height: "100%", width: "100%" }}
                    >
                      <TileLayer
                        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      />
                      <Polygon
                        positions={selectedParcel.polygon}
                        pathOptions={{
                          color: getParcelColor(selectedParcel),
                          fillColor: getParcelColor(selectedParcel),
                          fillOpacity: 0.5,
                          weight: 3
                        }}
                      />
                      <Marker position={selectedParcel.coordinates}>
                        <Popup>
                          <div className="text-center">
                            <h4 className="font-medium">{selectedParcel.numero}</h4>
                            <p className="text-sm">{selectedParcel.proprietaire}</p>
                          </div>
                        </Popup>
                      </Marker>
                    </MapContainer>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
