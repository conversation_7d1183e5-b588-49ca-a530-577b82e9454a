"use client"

import type React from "react"

import { useState, useRef } from "react"
import { ocrService, OCRResult } from "@/lib/ocr-service"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Upload, Eye, CheckCircle, AlertCircle, Loader2, FileText, Trash2, MapPin, User, Calendar, DollarSign } from "lucide-react"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface DocumentUploadProps {
  user: User
}

interface UploadedFile {
  id: string
  name: string
  size: string
  type: string
  status: "uploading" | "processing" | "completed" | "error"
  progress: number
  ocrResult?: OCRResult
  file?: File
  expirationDate?: string
  isContract?: boolean
}

export function DocumentUpload({ user }: DocumentUploadProps) {
  // Check if user can upload documents
  const canUploadDocuments = user.role === "ADMIN" || user.role === "JURIDIQUE"

  const [selectedParcel, setSelectedParcel] = useState("")
  const [documentType, setDocumentType] = useState("")
  const [description, setDescription] = useState("")
  const [expirationDate, setExpirationDate] = useState("")
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>(() => {
    // Add sample documents for demonstration, especially for standard users
    return [
      {
        id: "sample-001",
        name: "Contrat_Location_TF123456.pdf",
        size: "2.4 MB",
        type: "application/pdf",
        status: "completed",
        progress: 100,
        isContract: true,
        expirationDate: "2024-12-15",
        ocrResult: {
          text: "CONTRAT DE LOCATION\n\nParcelle: TF-123456\nPropriétaire: Ahmed Benali\nLocataire: Société Pétrolière Tunisienne\nDurée: 5 ans\nDate d'expiration: 15/12/2024\nMontant: 50,000 TND/an",
          confidence: 94.2,
          processingTime: 1250,
          extractedData: {
            contractNumber: "CONT-2024-001",
            parcelNumber: "TF-123456",
            proprietaire: "Ahmed Benali",
            amounts: ["50,000 TND"],
            expirationDate: "15/12/2024",
            signatureDate: "15/12/2019",
            dates: ["15/12/2019", "15/12/2024"],
            locations: ["Sfax", "Tunisie"]
          }
        }
      },
      {
        id: "sample-002",
        name: "Permis_Exploitation_TF789012.pdf",
        size: "1.8 MB",
        type: "application/pdf",
        status: "completed",
        progress: 100,
        isContract: false,
        expirationDate: "2024-06-30",
        ocrResult: {
          text: "PERMIS D'EXPLOITATION PÉTROLIÈRE\n\nNuméro: PERM-2024-005\nParcelle: TF-789012\nTitulaire: Fatima Alaoui\nActivité: Exploration pétrolière\nValidité: 30/06/2024",
          confidence: 91.8,
          processingTime: 980,
          extractedData: {
            contractNumber: "PERM-2024-005",
            parcelNumber: "TF-789012",
            proprietaire: "Fatima Alaoui",
            expirationDate: "30/06/2024",
            signatureDate: "01/07/2021",
            dates: ["01/07/2021", "30/06/2024"],
            locations: ["Gabès", "Tunisie"]
          }
        }
      },
      {
        id: "sample-003",
        name: "Etude_Geologique_TF345678.pdf",
        size: "5.2 MB",
        type: "application/pdf",
        status: "completed",
        progress: 100,
        isContract: false,
        expirationDate: "2025-03-20",
        ocrResult: {
          text: "ÉTUDE GÉOLOGIQUE\n\nRéférence: ETU-2024-012\nParcelle: TF-345678\nPropriétaire: Omar Tazi\nType: Analyse géologique approfondie\nValidité: 20/03/2025",
          confidence: 89.5,
          processingTime: 1850,
          extractedData: {
            contractNumber: "ETU-2024-012",
            parcelNumber: "TF-345678",
            proprietaire: "Omar Tazi",
            expirationDate: "20/03/2025",
            signatureDate: "20/03/2022",
            dates: ["20/03/2022", "20/03/2025"],
            locations: ["Tataouine", "Tunisie"]
          }
        }
      }
    ]
  })
  const [dragActive, setDragActive] = useState(false)
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const parcels = [
    { id: "P-2024-001", numero: "TF-123456", proprietaire: "Ahmed Benali" },
    { id: "P-2024-002", numero: "TF-789012", proprietaire: "Fatima Alaoui" },
    { id: "P-2024-003", numero: "TF-345678", proprietaire: "Omar Tazi" },
  ]

  const documentTypes = [
    "Contrat de Vente",
    "Acte de Propriété",
    "Plan Cadastral",
    "Expertise Technique",
    "Contrat de Location",
    "Document Juridique",
    "Autre",
  ]

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileUpload = async (files: FileList | null) => {
    if (!files) return

    // Show success message
    setShowSuccessMessage(true)
    setTimeout(() => setShowSuccessMessage(false), 3000)

    Array.from(files).forEach(async (file) => {
      const fileId = Math.random().toString(36).substring(2, 9)
      const isContract = documentType === "Contrat" || file.name.toLowerCase().includes('contrat')
      const hasExpiration = expirationDate && expirationDate.trim() !== ""
      const newFile: UploadedFile = {
        id: fileId,
        name: file.name,
        size: (file.size / 1024 / 1024).toFixed(2) + " MB",
        type: file.type,
        status: "uploading",
        progress: 0,
        file: file,
        isContract: isContract,
        expirationDate: hasExpiration ? expirationDate : undefined,
      }

      setUploadedFiles((prev) => [...prev, newFile])

      // Simulate upload progress
      const uploadInterval = setInterval(() => {
        setUploadedFiles((prev) =>
          prev.map((f) => {
            if (f.id === fileId && f.status === "uploading") {
              const newProgress = f.progress + Math.random() * 20
              if (newProgress >= 100) {
                clearInterval(uploadInterval)
                // Start OCR processing
                setTimeout(() => {
                  setUploadedFiles((prev) =>
                    prev.map((file) => (file.id === fileId ? { ...file, status: "processing", progress: 0 } : file)),
                  )

                  // Simulate OCR processing
                  const ocrInterval = setInterval(() => {
                    setUploadedFiles((prev) =>
                      prev.map((f) => {
                        if (f.id === fileId && f.status === "processing") {
                          const newProgress = f.progress + Math.random() * 15
                          if (newProgress >= 100) {
                            clearInterval(ocrInterval)
                            // Process OCR for the file
                            const processOCR = async () => {
                              try {
                                let ocrResult: OCRResult
                                if (f.file) {
                                  if (f.file.type.startsWith('image/')) {
                                    ocrResult = await ocrService.extractTextFromImage(f.file)
                                  } else {
                                    ocrResult = await ocrService.extractTextFromPDF(f.file)
                                  }

                                  setUploadedFiles((prev) =>
                                    prev.map((file) =>
                                      file.id === fileId ? { ...file, ocrResult, status: "completed", progress: 100 } : file
                                    )
                                  )
                                }
                              } catch (error) {
                                console.error('OCR failed:', error)
                                setUploadedFiles((prev) =>
                                  prev.map((file) =>
                                    file.id === fileId ? { ...file, status: "error", progress: 100 } : file
                                  )
                                )
                              }
                            }

                            processOCR()

                            return {
                              ...f,
                              status: "processing",
                              progress: 100,
                            }
                          }
                          return { ...f, progress: newProgress }
                        }
                        return f
                      }),
                    )
                  }, 500)
                }, 1000)

                return { ...f, status: "uploading", progress: 100 }
              }
              return { ...f, progress: newProgress }
            }
            return f
          }),
        )
      }, 300)
    })
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    handleFileUpload(e.dataTransfer.files)
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId))
  }

  const getStatusIcon = (status: UploadedFile["status"]) => {
    switch (status) {
      case "uploading":
        return <Upload className="h-4 w-4 text-blue-500" />
      case "processing":
        return <Loader2 className="h-4 w-4 text-orange-500 animate-spin" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusText = (status: UploadedFile["status"]) => {
    switch (status) {
      case "uploading":
        return "Upload en cours..."
      case "processing":
        return "Traitement OCR..."
      case "completed":
        return "Terminé"
      case "error":
        return "Erreur"
    }
  }

  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
        return <FileText className="h-4 w-4 text-blue-500" />
      case 'doc':
      case 'docx':
        return <FileText className="h-4 w-4 text-blue-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  const getExpirationStatus = (expirationDate: string) => {
    if (!expirationDate) return null

    const today = new Date()
    const expDate = new Date(expirationDate)
    const diffTime = expDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays < 0) {
      return { status: "expired", message: "Expiré", color: "text-red-600", bgColor: "bg-red-50", borderColor: "border-red-200" }
    } else if (diffDays <= 30) {
      return { status: "expiring", message: `Expire dans ${diffDays} jour(s)`, color: "text-orange-600", bgColor: "bg-orange-50", borderColor: "border-orange-200" }
    } else if (diffDays <= 90) {
      return { status: "warning", message: `Expire dans ${diffDays} jour(s)`, color: "text-yellow-600", bgColor: "bg-yellow-50", borderColor: "border-yellow-200" }
    } else {
      return { status: "valid", message: `Expire dans ${diffDays} jour(s)`, color: "text-green-600", bgColor: "bg-green-50", borderColor: "border-green-200" }
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-5 w-5" />
            <span>{canUploadDocuments ? "Upload de Documents" : "Consultation des Documents"}</span>
          </CardTitle>
          <CardDescription>
            {canUploadDocuments
              ? "Téléchargez et associez des documents aux parcelles avec traitement OCR automatique"
              : "Consultez les documents uploadés et leurs informations OCR (accès en lecture seule)"
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Access restriction message for standard users */}
          {!canUploadDocuments && (
            <Alert className="border-blue-200 bg-blue-50">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <strong>Accès en lecture seule:</strong> En tant qu'utilisateur standard, vous pouvez consulter les documents uploadés mais ne pouvez pas en télécharger de nouveaux. Contactez un administrateur ou un juriste pour uploader des documents.
              </AlertDescription>
            </Alert>
          )}

          {/* Form Fields - Only show for users who can upload */}
          {canUploadDocuments && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="parcel">Parcelle Associée</Label>
              <Select value={selectedParcel} onValueChange={setSelectedParcel}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une parcelle" />
                </SelectTrigger>
                <SelectContent>
                  {parcels.map((parcel) => (
                    <SelectItem key={parcel.id} value={parcel.id}>
                      {parcel.numero} - {parcel.proprietaire}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="docType">Type de Document</Label>
              <Select value={documentType} onValueChange={setDocumentType}>
                <SelectTrigger>
                  <SelectValue placeholder="Type de document" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description (Optionnel)</Label>
            <Textarea
              id="description"
              placeholder="Description du document..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>

          {/* Document Expiration Date */}
          <div>
            <Label htmlFor="expirationDate" className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Date d'Expiration du Document</span>
              {(documentType === "Contrat" || documentType === "Acte de Propriété" || documentType === "Permis" || documentType === "Autorisation") && (
                <Badge variant="destructive" className="text-xs">Obligatoire</Badge>
              )}
              {!(documentType === "Contrat" || documentType === "Acte de Propriété" || documentType === "Permis" || documentType === "Autorisation") && (
                <Badge variant="secondary" className="text-xs">Optionnel</Badge>
              )}
            </Label>
            <Input
              id="expirationDate"
              type="date"
              value={expirationDate}
              onChange={(e) => setExpirationDate(e.target.value)}
              className="mt-1"
              min={new Date().toISOString().split('T')[0]} // Minimum date is today
              placeholder="Sélectionner une date d'expiration"
            />
            <div className="mt-2 space-y-1">
              <p className="text-xs text-gray-500">
                Cette date sera utilisée pour les alertes d'expiration automatiques
              </p>
              {documentType && (
                <div className="text-xs text-blue-600">
                  <strong>Recommandations pour {documentType}:</strong>
                  {documentType === "Contrat" && " Généralement 1-5 ans selon les termes"}
                  {documentType === "Permis" && " Vérifiez la durée de validité officielle"}
                  {documentType === "Autorisation" && " Consultez les conditions d'autorisation"}
                  {documentType === "Acte de Propriété" && " Permanent sauf conditions spéciales"}
                  {documentType === "Plan Cadastral" && " Généralement valide 2-3 ans"}
                  {documentType === "Étude Géologique" && " Validité technique 3-5 ans"}
                  {documentType === "Rapport d'Expertise" && " Validité 1-2 ans selon le type"}
                  {!["Contrat", "Permis", "Autorisation", "Acte de Propriété", "Plan Cadastral", "Étude Géologique", "Rapport d'Expertise"].includes(documentType) && " Définissez selon la nature du document"}
                </div>
              )}
            </div>
          </div>

          {/* Success Message */}
          {showSuccessMessage && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <strong>Fichier(s) uploadé(s) avec succès!</strong> Vos fichiers ont été ajoutés à la liste et sont en cours de traitement.
              </AlertDescription>
            </Alert>
          )}

          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
              dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={handleFileSelect}
          >
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">Glissez-déposez vos fichiers ici</h3>
            <p className="text-gray-600 mb-4">ou cliquez pour sélectionner des fichiers</p>
            <Input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              onChange={(e) => handleFileUpload(e.target.files)}
              className="hidden"
              id="file-upload"
            />
            <Button variant="outline" className="pointer-events-none">
              Sélectionner des fichiers
            </Button>
            <p className="text-xs text-gray-500 mt-2">Formats supportés: PDF, JPEG, PNG, DOC, DOCX (Max 10MB par fichier)</p>
          </div>

          {/* OCR Info */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Traitement OCR automatique:</strong> Les documents uploadés seront automatiquement traités par
              notre système OCR avec support de l'arabe (Tesseract.js). Le texte extrait sera sauvegardé en base de
              données et dans un fichier .txt.
            </AlertDescription>
          </Alert>
          </>
          )}
        </CardContent>
      </Card>

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Fichiers Uploadés</span>
              <Badge variant="secondary">
                {uploadedFiles.length} fichier{uploadedFiles.length > 1 ? 's' : ''}
              </Badge>
            </CardTitle>
            <CardDescription>
              {canUploadDocuments
                ? "Statut du traitement des documents - Cliquez sur l'icône de suppression pour retirer un fichier"
                : "Consultation des documents uploadés et de leurs informations OCR (accès en lecture seule)"
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {uploadedFiles.map((file) => (
                <div key={file.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      {getFileTypeIcon(file.name)}
                      {getStatusIcon(file.status)}
                      <div>
                        <p className="font-medium text-sm">{file.name}</p>
                        <p className="text-xs text-gray-500">{file.size} • {file.type}</p>
                        {(file.isContract || file.expirationDate) && (
                          <div className="flex items-center space-x-1 mt-1">
                            {file.isContract && (
                              <Badge variant="outline" className="text-xs">
                                Contrat
                              </Badge>
                            )}
                            {file.expirationDate && (
                              <Badge variant="secondary" className="text-xs">
                                Expire: {new Date(file.expirationDate).toLocaleDateString('fr-FR')}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="text-right">
                        <p className="text-sm font-medium">{getStatusText(file.status)}</p>
                        <p className="text-xs text-gray-500">{Math.round(file.progress)}%</p>
                      </div>
                      {canUploadDocuments && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeFile(file.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  <Progress value={file.progress} className="mb-3" />

                  {/* Document Expiration Warning */}
                  {file.expirationDate && file.status === "completed" && (
                    (() => {
                      const expirationStatus = getExpirationStatus(file.expirationDate)
                      if (!expirationStatus) return null

                      return (
                        <div className={`p-3 rounded-lg border ${expirationStatus.bgColor} ${expirationStatus.borderColor} mb-3`}>
                          <div className="flex items-center space-x-2">
                            <AlertCircle className={`h-4 w-4 ${expirationStatus.color}`} />
                            <div>
                              <p className={`text-sm font-medium ${expirationStatus.color}`}>
                                Statut d'Expiration
                              </p>
                              <p className={`text-xs ${expirationStatus.color}`}>
                                {expirationStatus.message} - {new Date(file.expirationDate).toLocaleDateString('fr-FR')}
                              </p>
                              {expirationStatus.status === "expired" && (
                                <p className="text-xs text-red-600 mt-1">
                                  ⚠️ Ce document a expiré et nécessite une mise à jour immédiate
                                </p>
                              )}
                              {expirationStatus.status === "expiring" && (
                                <p className="text-xs text-orange-600 mt-1">
                                  🔔 Renouvellement ou mise à jour requis prochainement
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      )
                    })()
                  )}

                  {file.ocrResult && (
                    <div className="mt-3 space-y-3">
                      {/* OCR Summary */}
                      <div className="p-3 bg-blue-50 rounded border border-blue-200">
                        <div className="flex items-center justify-between mb-2">
                          <Label className="text-sm font-medium text-blue-800">Résultats OCR</Label>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              Confiance: {file.ocrResult.confidence.toFixed(1)}%
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {file.ocrResult.processingTime}ms
                            </Badge>
                          </div>
                        </div>

                        {/* Extracted Data */}
                        {file.ocrResult.extractedData && (
                          <div className="grid grid-cols-2 gap-3 text-xs">
                            {file.ocrResult.extractedData.contractNumber && (
                              <div className="flex items-center space-x-1">
                                <FileText className="h-3 w-3 text-blue-600" />
                                <span className="font-medium">Contrat:</span>
                                <span>{file.ocrResult.extractedData.contractNumber}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.parcelNumber && (
                              <div className="flex items-center space-x-1">
                                <MapPin className="h-3 w-3 text-green-600" />
                                <span className="font-medium">Parcelle:</span>
                                <span>{file.ocrResult.extractedData.parcelNumber}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.proprietaire && (
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3 text-purple-600" />
                                <span className="font-medium">Propriétaire:</span>
                                <span>{file.ocrResult.extractedData.proprietaire}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.amounts && file.ocrResult.extractedData.amounts.length > 0 && (
                              <div className="flex items-center space-x-1">
                                <DollarSign className="h-3 w-3 text-green-600" />
                                <span className="font-medium">Montants:</span>
                                <span>{file.ocrResult.extractedData.amounts.join(', ')}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.expirationDate && (
                              <div className="col-span-2 flex items-center space-x-1">
                                <AlertCircle className="h-3 w-3 text-red-600" />
                                <span className="font-medium">Date d'expiration:</span>
                                <span className="text-red-600 font-medium">{file.ocrResult.extractedData.expirationDate}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.signatureDate && (
                              <div className="col-span-2 flex items-center space-x-1">
                                <Calendar className="h-3 w-3 text-blue-600" />
                                <span className="font-medium">Date de signature:</span>
                                <span>{file.ocrResult.extractedData.signatureDate}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.dates && file.ocrResult.extractedData.dates.length > 0 && (
                              <div className="col-span-2 flex items-center space-x-1">
                                <Calendar className="h-3 w-3 text-orange-600" />
                                <span className="font-medium">Autres dates:</span>
                                <span>{file.ocrResult.extractedData.dates.join(', ')}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.locations && file.ocrResult.extractedData.locations.length > 0 && (
                              <div className="col-span-2 flex items-center space-x-1">
                                <MapPin className="h-3 w-3 text-red-600" />
                                <span className="font-medium">Lieux:</span>
                                <span>{file.ocrResult.extractedData.locations.join(', ')}</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Full OCR Text */}
                      <div className="p-3 bg-gray-50 rounded border">
                        <div className="flex items-center justify-between mb-2">
                          <Label className="text-sm font-medium">Texte Complet Extrait:</Label>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            Voir le fichier .txt
                          </Button>
                        </div>
                        <div className="max-h-32 overflow-y-auto">
                          <p className="text-sm text-gray-700 whitespace-pre-line">{file.ocrResult.text}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
