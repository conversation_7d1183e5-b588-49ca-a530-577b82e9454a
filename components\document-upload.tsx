"use client"

import type React from "react"

import { useState, useRef } from "react"
import { ocrService, OCRResult } from "@/lib/ocr-service"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Upload, Eye, CheckCircle, AlertCircle, Loader2, FileText, Trash2, MapPin, User, Calendar, DollarSign } from "lucide-react"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface DocumentUploadProps {
  user: User
}

interface UploadedFile {
  id: string
  name: string
  size: string
  type: string
  status: "uploading" | "processing" | "completed" | "error"
  progress: number
  ocrResult?: OCRResult
  file?: File
}

export function DocumentUpload({ user }: DocumentUploadProps) {
  const [selectedParcel, setSelectedParcel] = useState("")
  const [documentType, setDocumentType] = useState("")
  const [description, setDescription] = useState("")
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [dragActive, setDragActive] = useState(false)
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const parcels = [
    { id: "P-2024-001", numero: "TF-123456", proprietaire: "Ahmed Benali" },
    { id: "P-2024-002", numero: "TF-789012", proprietaire: "Fatima Alaoui" },
    { id: "P-2024-003", numero: "TF-345678", proprietaire: "Omar Tazi" },
  ]

  const documentTypes = [
    "Contrat de Vente",
    "Acte de Propriété",
    "Plan Cadastral",
    "Expertise Technique",
    "Contrat de Location",
    "Document Juridique",
    "Autre",
  ]

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileUpload = async (files: FileList | null) => {
    if (!files) return

    // Show success message
    setShowSuccessMessage(true)
    setTimeout(() => setShowSuccessMessage(false), 3000)

    Array.from(files).forEach(async (file) => {
      const fileId = Math.random().toString(36).substring(2, 9)
      const newFile: UploadedFile = {
        id: fileId,
        name: file.name,
        size: (file.size / 1024 / 1024).toFixed(2) + " MB",
        type: file.type,
        status: "uploading",
        progress: 0,
        file: file,
      }

      setUploadedFiles((prev) => [...prev, newFile])

      // Simulate upload progress
      const uploadInterval = setInterval(() => {
        setUploadedFiles((prev) =>
          prev.map((f) => {
            if (f.id === fileId && f.status === "uploading") {
              const newProgress = f.progress + Math.random() * 20
              if (newProgress >= 100) {
                clearInterval(uploadInterval)
                // Start OCR processing
                setTimeout(() => {
                  setUploadedFiles((prev) =>
                    prev.map((file) => (file.id === fileId ? { ...file, status: "processing", progress: 0 } : file)),
                  )

                  // Simulate OCR processing
                  const ocrInterval = setInterval(() => {
                    setUploadedFiles((prev) =>
                      prev.map((f) => {
                        if (f.id === fileId && f.status === "processing") {
                          const newProgress = f.progress + Math.random() * 15
                          if (newProgress >= 100) {
                            clearInterval(ocrInterval)
                            // Process OCR for the file
                            const processOCR = async () => {
                              try {
                                let ocrResult: OCRResult
                                if (f.file) {
                                  if (f.file.type.startsWith('image/')) {
                                    ocrResult = await ocrService.extractTextFromImage(f.file)
                                  } else {
                                    ocrResult = await ocrService.extractTextFromPDF(f.file)
                                  }

                                  setUploadedFiles((prev) =>
                                    prev.map((file) =>
                                      file.id === fileId ? { ...file, ocrResult, status: "completed", progress: 100 } : file
                                    )
                                  )
                                }
                              } catch (error) {
                                console.error('OCR failed:', error)
                                setUploadedFiles((prev) =>
                                  prev.map((file) =>
                                    file.id === fileId ? { ...file, status: "error", progress: 100 } : file
                                  )
                                )
                              }
                            }

                            processOCR()

                            return {
                              ...f,
                              status: "processing",
                              progress: 100,
                            }
                          }
                          return { ...f, progress: newProgress }
                        }
                        return f
                      }),
                    )
                  }, 500)
                }, 1000)

                return { ...f, status: "uploading", progress: 100 }
              }
              return { ...f, progress: newProgress }
            }
            return f
          }),
        )
      }, 300)
    })
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    handleFileUpload(e.dataTransfer.files)
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId))
  }

  const getStatusIcon = (status: UploadedFile["status"]) => {
    switch (status) {
      case "uploading":
        return <Upload className="h-4 w-4 text-blue-500" />
      case "processing":
        return <Loader2 className="h-4 w-4 text-orange-500 animate-spin" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusText = (status: UploadedFile["status"]) => {
    switch (status) {
      case "uploading":
        return "Upload en cours..."
      case "processing":
        return "Traitement OCR..."
      case "completed":
        return "Terminé"
      case "error":
        return "Erreur"
    }
  }

  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
        return <FileText className="h-4 w-4 text-blue-500" />
      case 'doc':
      case 'docx':
        return <FileText className="h-4 w-4 text-blue-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-5 w-5" />
            <span>Upload de Documents</span>
          </CardTitle>
          <CardDescription>
            Téléchargez et associez des documents aux parcelles avec traitement OCR automatique
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Form Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="parcel">Parcelle Associée</Label>
              <Select value={selectedParcel} onValueChange={setSelectedParcel}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une parcelle" />
                </SelectTrigger>
                <SelectContent>
                  {parcels.map((parcel) => (
                    <SelectItem key={parcel.id} value={parcel.id}>
                      {parcel.numero} - {parcel.proprietaire}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="docType">Type de Document</Label>
              <Select value={documentType} onValueChange={setDocumentType}>
                <SelectTrigger>
                  <SelectValue placeholder="Type de document" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description (Optionnel)</Label>
            <Textarea
              id="description"
              placeholder="Description du document..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>

          {/* Success Message */}
          {showSuccessMessage && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <strong>Fichier(s) uploadé(s) avec succès!</strong> Vos fichiers ont été ajoutés à la liste et sont en cours de traitement.
              </AlertDescription>
            </Alert>
          )}

          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
              dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={handleFileSelect}
          >
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">Glissez-déposez vos fichiers ici</h3>
            <p className="text-gray-600 mb-4">ou cliquez pour sélectionner des fichiers</p>
            <Input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              onChange={(e) => handleFileUpload(e.target.files)}
              className="hidden"
              id="file-upload"
            />
            <Button variant="outline" className="pointer-events-none">
              Sélectionner des fichiers
            </Button>
            <p className="text-xs text-gray-500 mt-2">Formats supportés: PDF, JPEG, PNG, DOC, DOCX (Max 10MB par fichier)</p>
          </div>

          {/* OCR Info */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Traitement OCR automatique:</strong> Les documents uploadés seront automatiquement traités par
              notre système OCR avec support de l'arabe (Tesseract.js). Le texte extrait sera sauvegardé en base de
              données et dans un fichier .txt.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Fichiers Uploadés</span>
              <Badge variant="secondary">
                {uploadedFiles.length} fichier{uploadedFiles.length > 1 ? 's' : ''}
              </Badge>
            </CardTitle>
            <CardDescription>
              Statut du traitement des documents - Cliquez sur l'icône de suppression pour retirer un fichier
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {uploadedFiles.map((file) => (
                <div key={file.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      {getFileTypeIcon(file.name)}
                      {getStatusIcon(file.status)}
                      <div>
                        <p className="font-medium text-sm">{file.name}</p>
                        <p className="text-xs text-gray-500">{file.size} • {file.type}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="text-right">
                        <p className="text-sm font-medium">{getStatusText(file.status)}</p>
                        <p className="text-xs text-gray-500">{Math.round(file.progress)}%</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <Progress value={file.progress} className="mb-3" />

                  {file.ocrResult && (
                    <div className="mt-3 space-y-3">
                      {/* OCR Summary */}
                      <div className="p-3 bg-blue-50 rounded border border-blue-200">
                        <div className="flex items-center justify-between mb-2">
                          <Label className="text-sm font-medium text-blue-800">Résultats OCR</Label>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              Confiance: {file.ocrResult.confidence.toFixed(1)}%
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {file.ocrResult.processingTime}ms
                            </Badge>
                          </div>
                        </div>

                        {/* Extracted Data */}
                        {file.ocrResult.extractedData && (
                          <div className="grid grid-cols-2 gap-3 text-xs">
                            {file.ocrResult.extractedData.contractNumber && (
                              <div className="flex items-center space-x-1">
                                <FileText className="h-3 w-3 text-blue-600" />
                                <span className="font-medium">Contrat:</span>
                                <span>{file.ocrResult.extractedData.contractNumber}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.parcelNumber && (
                              <div className="flex items-center space-x-1">
                                <MapPin className="h-3 w-3 text-green-600" />
                                <span className="font-medium">Parcelle:</span>
                                <span>{file.ocrResult.extractedData.parcelNumber}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.proprietaire && (
                              <div className="flex items-center space-x-1">
                                <User className="h-3 w-3 text-purple-600" />
                                <span className="font-medium">Propriétaire:</span>
                                <span>{file.ocrResult.extractedData.proprietaire}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.amounts && file.ocrResult.extractedData.amounts.length > 0 && (
                              <div className="flex items-center space-x-1">
                                <DollarSign className="h-3 w-3 text-green-600" />
                                <span className="font-medium">Montants:</span>
                                <span>{file.ocrResult.extractedData.amounts.join(', ')}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.dates && file.ocrResult.extractedData.dates.length > 0 && (
                              <div className="col-span-2 flex items-center space-x-1">
                                <Calendar className="h-3 w-3 text-orange-600" />
                                <span className="font-medium">Dates:</span>
                                <span>{file.ocrResult.extractedData.dates.join(', ')}</span>
                              </div>
                            )}
                            {file.ocrResult.extractedData.locations && file.ocrResult.extractedData.locations.length > 0 && (
                              <div className="col-span-2 flex items-center space-x-1">
                                <MapPin className="h-3 w-3 text-red-600" />
                                <span className="font-medium">Lieux:</span>
                                <span>{file.ocrResult.extractedData.locations.join(', ')}</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Full OCR Text */}
                      <div className="p-3 bg-gray-50 rounded border">
                        <div className="flex items-center justify-between mb-2">
                          <Label className="text-sm font-medium">Texte Complet Extrait:</Label>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            Voir le fichier .txt
                          </Button>
                        </div>
                        <div className="max-h-32 overflow-y-auto">
                          <p className="text-sm text-gray-700 whitespace-pre-line">{file.ocrResult.text}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
