"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, Eye, CheckCircle, AlertCircle, Loader2 } from "lucide-react"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface DocumentUploadProps {
  user: User
}

interface UploadedFile {
  id: string
  name: string
  size: string
  type: string
  status: "uploading" | "processing" | "completed" | "error"
  progress: number
  ocrText?: string
}

export function DocumentUpload({ user }: DocumentUploadProps) {
  const [selectedParcel, setSelectedParcel] = useState("")
  const [documentType, setDocumentType] = useState("")
  const [description, setDescription] = useState("")
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [dragActive, setDragActive] = useState(false)

  const parcels = [
    { id: "P-2024-001", numero: "TF-123456", proprietaire: "Ahmed Benali" },
    { id: "P-2024-002", numero: "TF-789012", proprietaire: "Fatima Alaoui" },
    { id: "P-2024-003", numero: "TF-345678", proprietaire: "Omar Tazi" },
  ]

  const documentTypes = [
    "Contrat de Vente",
    "Acte de Propriété",
    "Plan Cadastral",
    "Expertise Technique",
    "Contrat de Location",
    "Document Juridique",
    "Autre",
  ]

  const handleFileUpload = (files: FileList | null) => {
    if (!files) return

    Array.from(files).forEach((file) => {
      const fileId = Math.random().toString(36).substr(2, 9)
      const newFile: UploadedFile = {
        id: fileId,
        name: file.name,
        size: (file.size / 1024 / 1024).toFixed(2) + " MB",
        type: file.type,
        status: "uploading",
        progress: 0,
      }

      setUploadedFiles((prev) => [...prev, newFile])

      // Simulate upload progress
      const uploadInterval = setInterval(() => {
        setUploadedFiles((prev) =>
          prev.map((f) => {
            if (f.id === fileId && f.status === "uploading") {
              const newProgress = f.progress + Math.random() * 20
              if (newProgress >= 100) {
                clearInterval(uploadInterval)
                // Start OCR processing
                setTimeout(() => {
                  setUploadedFiles((prev) =>
                    prev.map((file) => (file.id === fileId ? { ...file, status: "processing", progress: 0 } : file)),
                  )

                  // Simulate OCR processing
                  const ocrInterval = setInterval(() => {
                    setUploadedFiles((prev) =>
                      prev.map((f) => {
                        if (f.id === fileId && f.status === "processing") {
                          const newProgress = f.progress + Math.random() * 15
                          if (newProgress >= 100) {
                            clearInterval(ocrInterval)
                            return {
                              ...f,
                              status: "completed",
                              progress: 100,
                              ocrText:
                                "نص مستخرج من الوثيقة باللغة العربية\nContenu extrait du document\nTexte reconnu par OCR avec support arabe",
                            }
                          }
                          return { ...f, progress: newProgress }
                        }
                        return f
                      }),
                    )
                  }, 500)
                }, 1000)

                return { ...f, status: "uploading", progress: 100 }
              }
              return { ...f, progress: newProgress }
            }
            return f
          }),
        )
      }, 300)
    })
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    handleFileUpload(e.dataTransfer.files)
  }

  const getStatusIcon = (status: UploadedFile["status"]) => {
    switch (status) {
      case "uploading":
        return <Upload className="h-4 w-4 text-blue-500" />
      case "processing":
        return <Loader2 className="h-4 w-4 text-orange-500 animate-spin" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusText = (status: UploadedFile["status"]) => {
    switch (status) {
      case "uploading":
        return "Upload en cours..."
      case "processing":
        return "Traitement OCR..."
      case "completed":
        return "Terminé"
      case "error":
        return "Erreur"
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-5 w-5" />
            <span>Upload de Documents</span>
          </CardTitle>
          <CardDescription>
            Téléchargez et associez des documents aux parcelles avec traitement OCR automatique
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Form Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="parcel">Parcelle Associée</Label>
              <Select value={selectedParcel} onValueChange={setSelectedParcel}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une parcelle" />
                </SelectTrigger>
                <SelectContent>
                  {parcels.map((parcel) => (
                    <SelectItem key={parcel.id} value={parcel.id}>
                      {parcel.numero} - {parcel.proprietaire}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="docType">Type de Document</Label>
              <Select value={documentType} onValueChange={setDocumentType}>
                <SelectTrigger>
                  <SelectValue placeholder="Type de document" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description (Optionnel)</Label>
            <Textarea
              id="description"
              placeholder="Description du document..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </div>

          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">Glissez-déposez vos fichiers ici</h3>
            <p className="text-gray-600 mb-4">ou cliquez pour sélectionner des fichiers</p>
            <Input
              type="file"
              multiple
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => handleFileUpload(e.target.files)}
              className="hidden"
              id="file-upload"
            />
            <Label htmlFor="file-upload">
              <Button variant="outline" className="cursor-pointer">
                Sélectionner des fichiers
              </Button>
            </Label>
            <p className="text-xs text-gray-500 mt-2">Formats supportés: PDF, JPEG, PNG (Max 10MB par fichier)</p>
          </div>

          {/* OCR Info */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Traitement OCR automatique:</strong> Les documents uploadés seront automatiquement traités par
              notre système OCR avec support de l'arabe (Tesseract.js). Le texte extrait sera sauvegardé en base de
              données et dans un fichier .txt.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Fichiers Uploadés</CardTitle>
            <CardDescription>Statut du traitement des documents</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {uploadedFiles.map((file) => (
                <div key={file.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(file.status)}
                      <div>
                        <p className="font-medium text-sm">{file.name}</p>
                        <p className="text-xs text-gray-500">{file.size}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{getStatusText(file.status)}</p>
                      <p className="text-xs text-gray-500">{Math.round(file.progress)}%</p>
                    </div>
                  </div>

                  <Progress value={file.progress} className="mb-3" />

                  {file.ocrText && (
                    <div className="mt-3 p-3 bg-gray-50 rounded border">
                      <div className="flex items-center justify-between mb-2">
                        <Label className="text-sm font-medium">Texte OCR Extrait:</Label>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          Voir le fichier .txt
                        </Button>
                      </div>
                      <p className="text-sm text-gray-700 whitespace-pre-line">{file.ocrText}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
