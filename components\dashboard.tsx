"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { FileText, MapPin, Users, Upload, TrendingUp, AlertCircle } from "lucide-react"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface DashboardProps {
  user: User
}

export function Dashboard({ user }: DashboardProps) {
  const stats = {
    totalParcels: 3,
    totalContracts: 8,
    activeContracts: 6,
    pendingDocuments: 2,
    ocrProcessed: 12,
    recentUploads: 5,
  }

  const recentActivities = [
    { id: 1, action: "Document uploadé", parcel: "TF-123456 (P-2024-001)", time: "Il y a 1h", type: "ocr" },
    { id: 2, action: "Contrat de vente traité", parcel: "TF-789012 (P-2024-002)", time: "Il y a 3h", type: "contract" },
    { id: 3, action: "Plan cadastral ajouté", parcel: "TF-345678 (P-2024-003)", time: "Il y a 5h", type: "ocr" },
    { id: 4, action: "Utilisateur connecté", parcel: "Mehdi CHAARI", time: "Il y a 2h", type: "user" },
    { id: 5, action: "Fichier PDF analysé", parcel: "Contrat_Location_2024.pdf", time: "Il y a 6h", type: "ocr" },
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-green-600 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-2">Bienvenue, {user.nom}</h2>
        <p className="text-blue-100">
          Rôle:{" "}
          <Badge variant="secondary" className="ml-2">
            {user.role}
          </Badge>
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Parcelles</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalParcels.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Parcelles enregistrées dans le système</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contrats Actifs</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeContracts.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{stats.totalContracts} contrats au total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Documents OCR</CardTitle>
            <Upload className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.ocrProcessed.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{stats.pendingDocuments} fichiers en attente</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Uploads Récents</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentUploads}</div>
            <p className="text-xs text-muted-foreground">Nouveaux fichiers cette semaine</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Activités Récentes</CardTitle>
            <CardDescription>Dernières actions effectuées sur le système</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {activity.type === "contract" && <FileText className="h-4 w-4 text-blue-500" />}
                    {activity.type === "ocr" && <Upload className="h-4 w-4 text-green-500" />}
                    {activity.type === "parcel" && <MapPin className="h-4 w-4 text-orange-500" />}
                    {activity.type === "user" && <Users className="h-4 w-4 text-purple-500" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-sm text-gray-500">{activity.parcel}</p>
                  </div>
                  <div className="text-xs text-gray-400">{activity.time}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle>État du Système</CardTitle>
            <CardDescription>Performance et statut des services</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Base de Données</span>
                <Badge variant="default">Opérationnel</Badge>
              </div>
              <Progress value={25} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">25% d'utilisation - 3 parcelles</p>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Service OCR</span>
                <Badge variant="default">Actif</Badge>
              </div>
              <Progress value={35} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">35% de charge - 12 fichiers traités</p>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Stockage</span>
                <Badge variant="default">Optimal</Badge>
              </div>
              <Progress value={15} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">15% utilisé - 8 documents</p>
            </div>

            <div className="flex items-center space-x-2 p-3 bg-yellow-50 rounded-lg">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <p className="text-sm text-yellow-800">Maintenance programmée dimanche 3h-5h</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
