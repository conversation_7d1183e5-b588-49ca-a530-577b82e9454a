{"name": "gis-land-management-backend", "version": "1.0.0", "description": "Backend API for GIS Land Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "sequelize": "^6.35.0", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "tesseract.js": "^5.0.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}, "keywords": ["gis", "land-management", "contracts", "ocr", "postgis"], "author": "GIS Development Team", "license": "MIT"}