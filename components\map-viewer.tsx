"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { MapPin, FileText, Download, Eye, Search, Filter } from "lucide-react"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface Parcel {
  id: string
  numero: string
  coordinates: [number, number]
  proprietaire: string
  superficie: number
  statut: string
  documents: Document[]
}

interface Document {
  id: string
  nom: string
  type: string
  dateUpload: string
  statut: string
  taille: string
}

interface MapViewerProps {
  user: User
}

export function MapViewer({ user }: MapViewerProps) {
  const [selectedParcel, setSelectedParcel] = useState<Parcel | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [parcels] = useState<Parcel[]>([
    {
      id: "P-2024-001",
      numero: "TF-123456",
      coordinates: [33.5731, -7.5898], // Casablanca
      proprietaire: "Ahmed Benali",
      superficie: 2500,
      statut: "Actif",
      documents: [
        {
          id: "D1",
          nom: "Contrat_Vente_2024.pdf",
          type: "Contrat",
          dateUpload: "2024-01-15",
          statut: "Validé",
          taille: "2.3 MB",
        },
        {
          id: "D2",
          nom: "Plan_Cadastral.pdf",
          type: "Plan",
          dateUpload: "2024-01-10",
          statut: "En cours",
          taille: "1.8 MB",
        },
      ],
    },
    {
      id: "P-2024-002",
      numero: "TF-789012",
      coordinates: [34.0209, -6.8416], // Rabat
      proprietaire: "Fatima Alaoui",
      superficie: 1800,
      statut: "En cours",
      documents: [
        {
          id: "D3",
          nom: "Acte_Propriete.pdf",
          type: "Acte",
          dateUpload: "2024-01-20",
          statut: "Validé",
          taille: "1.5 MB",
        },
      ],
    },
    {
      id: "P-2024-003",
      numero: "TF-345678",
      coordinates: [31.6295, -7.9811], // Marrakech
      proprietaire: "Omar Tazi",
      superficie: 3200,
      statut: "Actif",
      documents: [
        {
          id: "D4",
          nom: "Contrat_Location.pdf",
          type: "Contrat",
          dateUpload: "2024-01-25",
          statut: "En attente",
          taille: "2.1 MB",
        },
        {
          id: "D5",
          nom: "Expertise_Technique.pdf",
          type: "Expertise",
          dateUpload: "2024-01-22",
          statut: "Validé",
          taille: "3.2 MB",
        },
      ],
    },
  ])

  const filteredParcels = parcels.filter(
    (parcel) =>
      parcel.numero.toLowerCase().includes(searchTerm.toLowerCase()) ||
      parcel.proprietaire.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleParcelClick = (parcel: Parcel) => {
    setSelectedParcel(parcel)
  }

  const handleDownloadDocument = (doc: Document) => {
    // Simulate document download
    console.log(`Téléchargement du document: ${doc.nom}`)
  }

  const handleViewDocument = (doc: Document) => {
    // Simulate document viewing
    console.log(`Visualisation du document: ${doc.nom}`)
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5" />
            <span>Carte Interactive des Parcelles</span>
          </CardTitle>
          <CardDescription>Cliquez sur une parcelle pour voir les détails et documents associés</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <div className="flex-1">
              <Label htmlFor="search">Rechercher une parcelle</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Numéro de parcelle ou propriétaire..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline" className="mt-6">
              <Filter className="h-4 w-4 mr-2" />
              Filtres
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Map Simulation */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Map Area */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Carte des Parcelles</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gradient-to-br from-green-100 to-blue-100 h-96 rounded-lg flex items-center justify-center relative overflow-hidden">
              <div className="text-center text-gray-600">
                <MapPin className="h-12 w-12 mx-auto mb-4 text-blue-500" />
                <p className="text-lg font-medium">Carte Interactive</p>
                <p className="text-sm">Intégration Leaflet + PostGIS</p>
              </div>

              {/* Simulated Parcel Markers */}
              <div className="absolute top-20 left-20">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-white shadow-lg"
                  onClick={() => handleParcelClick(parcels[0])}
                >
                  📍 {parcels[0].numero}
                </Button>
              </div>
              <div className="absolute top-32 right-24">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-white shadow-lg"
                  onClick={() => handleParcelClick(parcels[1])}
                >
                  📍 {parcels[1].numero}
                </Button>
              </div>
              <div className="absolute bottom-24 left-32">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-white shadow-lg"
                  onClick={() => handleParcelClick(parcels[2])}
                >
                  📍 {parcels[2].numero}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Parcels List */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Parcelles</CardTitle>
            <CardDescription>{filteredParcels.length} parcelle(s) trouvée(s)</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {filteredParcels.map((parcel) => (
                <div
                  key={parcel.id}
                  className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => handleParcelClick(parcel)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium">{parcel.numero}</h4>
                    <Badge variant={parcel.statut === "Actif" ? "default" : "secondary"}>{parcel.statut}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-1">Propriétaire: {parcel.proprietaire}</p>
                  <p className="text-sm text-gray-600 mb-2">Superficie: {parcel.superficie.toLocaleString()} m²</p>
                  <p className="text-xs text-gray-500">{parcel.documents.length} document(s) associé(s)</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Parcel Details Dialog */}
      <Dialog open={!!selectedParcel} onOpenChange={() => setSelectedParcel(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Détails de la Parcelle {selectedParcel?.numero}</span>
            </DialogTitle>
            <DialogDescription>Informations et documents associés à cette parcelle</DialogDescription>
          </DialogHeader>

          {selectedParcel && (
            <div className="space-y-6">
              {/* Parcel Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Propriétaire</Label>
                  <p className="text-sm text-gray-600">{selectedParcel.proprietaire}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Superficie</Label>
                  <p className="text-sm text-gray-600">{selectedParcel.superficie.toLocaleString()} m²</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Statut</Label>
                  <Badge variant={selectedParcel.statut === "Actif" ? "default" : "secondary"}>
                    {selectedParcel.statut}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">Coordonnées</Label>
                  <p className="text-sm text-gray-600">
                    {selectedParcel.coordinates[0]}, {selectedParcel.coordinates[1]}
                  </p>
                </div>
              </div>

              {/* Documents */}
              <div>
                <h4 className="font-medium mb-3 flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>Documents Associés ({selectedParcel.documents.length})</span>
                </h4>
                <div className="space-y-3">
                  {selectedParcel.documents.map((doc) => (
                    <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{doc.nom}</p>
                        <div className="flex items-center space-x-4 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {doc.type}
                          </Badge>
                          <span className="text-xs text-gray-500">{doc.dateUpload}</span>
                          <span className="text-xs text-gray-500">{doc.taille}</span>
                          <Badge
                            variant={
                              doc.statut === "Validé"
                                ? "default"
                                : doc.statut === "En cours"
                                  ? "secondary"
                                  : "destructive"
                            }
                            className="text-xs"
                          >
                            {doc.statut}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => handleViewDocument(doc)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDownloadDocument(doc)}>
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
