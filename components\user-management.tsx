"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Users, Plus, Edit, Trash2, Search } from "lucide-react"

interface User {
  id: string
  nom: string
  email: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
  dateCreation: string
  dernierAcces: string
  statut: "Actif" | "Inactif"
}

interface UserManagementProps {
  user: User
}

export function UserManagement({ user: currentUser }: UserManagementProps) {
  // Determine what roles the current user can create
  const canCreateRole = (role: string) => {
    if (currentUser.role === "ADMIN") {
      return true // Admin can create any role
    }
    if (currentUser.role === "JURIDIQUE") {
      return true // Juridique can create any role including ADMIN
    }
    return false // Standard users cannot create users
  }
  const [users, setUsers] = useState<User[]>([
    {
      id: "1",
      nom: "Adam SMIDA",
      email: "<EMAIL>",
      role: "ADMIN",
      dateCreation: "2024-01-01",
      dernierAcces: "2024-01-24",
      statut: "Actif",
    },
    {
      id: "2",
      nom: "Mehdi CHAARI",
      email: "<EMAIL>",
      role: "JURIDIQUE",
      dateCreation: "2024-01-05",
      dernierAcces: "2024-01-23",
      statut: "Actif",
    },
    {
      id: "3",
      nom: "Jihen KHEBOU",
      email: "<EMAIL>",
      role: "STANDARD",
      dateCreation: "2024-01-10",
      dernierAcces: "2024-01-22",
      statut: "Actif",
    },
    {
      id: "4",
      nom: "Aicha TAKALI",
      email: "<EMAIL>",
      role: "JURIDIQUE",
      dateCreation: "2024-01-15",
      dernierAcces: "2024-01-20",
      statut: "Inactif",
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [selectedRole, setSelectedRole] = useState<string>("all")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [newUser, setNewUser] = useState({
    nom: "",
    email: "",
    role: "STANDARD" as const,
    password: "",
  })

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = selectedRole === "all" || user.role === selectedRole
    return matchesSearch && matchesRole
  })

  const handleCreateUser = () => {
    const user: User = {
      id: Math.random().toString(36).substring(2, 11),
      nom: newUser.nom,
      email: newUser.email,
      role: newUser.role,
      dateCreation: new Date().toISOString().split("T")[0],
      dernierAcces: "-",
      statut: "Actif",
    }
    setUsers((prev) => [...prev, user])
    setNewUser({ nom: "", email: "", role: "STANDARD", password: "" })
    setIsCreateDialogOpen(false)
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
  }

  const handleUpdateUser = () => {
    if (!editingUser) return
    setUsers((prev) => prev.map((u) => (u.id === editingUser.id ? editingUser : u)))
    setEditingUser(null)
  }

  const handleDeleteUser = (userId: string) => {
    setUsers((prev) => prev.filter((u) => u.id !== userId))
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "default"
      case "JURIDIQUE":
        return "secondary"
      case "STANDARD":
        return "outline"
      default:
        return "outline"
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "Administrateur"
      case "JURIDIQUE":
        return "Juriste"
      case "STANDARD":
        return "Utilisateur"
      default:
        return role
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Gestion des Utilisateurs</span>
          </CardTitle>
          <CardDescription>
            Gérer les comptes utilisateurs et leurs permissions - Connecté en tant que {currentUser.role === "ADMIN" ? "Administrateur" : currentUser.role === "JURIDIQUE" ? "Juriste" : "Utilisateur"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par nom ou email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedRole} onValueChange={setSelectedRole}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par rôle" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les rôles</SelectItem>
                <SelectItem value="ADMIN">Administrateur</SelectItem>
                <SelectItem value="JURIDIQUE">Juriste</SelectItem>
                <SelectItem value="STANDARD">Utilisateur</SelectItem>
              </SelectContent>
            </Select>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Nouvel Utilisateur
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Créer un Nouvel Utilisateur</DialogTitle>
                  <DialogDescription>
                    Remplissez les informations pour créer un nouveau compte utilisateur
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="nom">Nom Complet</Label>
                    <Input
                      id="nom"
                      value={newUser.nom}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, nom: e.target.value }))}
                      placeholder="Nom et prénom"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="password">Mot de Passe</Label>
                    <Input
                      id="password"
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, password: e.target.value }))}
                      placeholder="••••••••"
                    />
                  </div>
                  <div>
                    <Label htmlFor="role">Rôle</Label>
                    <Select
                      value={newUser.role}
                      onValueChange={(value: any) => setNewUser((prev) => ({ ...prev, role: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="STANDARD">Utilisateur Standard</SelectItem>
                        <SelectItem value="JURIDIQUE">Juriste</SelectItem>
                        <SelectItem value="ADMIN">Administrateur</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-1">
                      {currentUser.role === "JURIDIQUE" && "En tant que Juriste, vous pouvez créer tous les types d'utilisateurs, y compris les Administrateurs."}
                      {currentUser.role === "ADMIN" && "En tant qu'Administrateur, vous pouvez créer tous les types d'utilisateurs."}
                    </p>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Annuler
                    </Button>
                    <Button onClick={handleCreateUser}>Créer l'Utilisateur</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Users Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nom</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Rôle</TableHead>
                  <TableHead>Date Création</TableHead>
                  <TableHead>Dernier Accès</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{user.nom}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Badge variant={getRoleBadgeVariant(user.role)}>{getRoleLabel(user.role)}</Badge>
                    </TableCell>
                    <TableCell>{user.dateCreation}</TableCell>
                    <TableCell>{user.dernierAcces}</TableCell>
                    <TableCell>
                      <Badge variant={user.statut === "Actif" ? "default" : "secondary"}>{user.statut}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => handleEditUser(user)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteUser(user.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              Aucun utilisateur trouvé avec les critères de recherche
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={!!editingUser} onOpenChange={() => setEditingUser(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Modifier l'Utilisateur</DialogTitle>
            <DialogDescription>Modifier les informations de l'utilisateur</DialogDescription>
          </DialogHeader>
          {editingUser && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-nom">Nom Complet</Label>
                <Input
                  id="edit-nom"
                  value={editingUser.nom}
                  onChange={(e) => setEditingUser((prev) => (prev ? { ...prev, nom: e.target.value } : null))}
                />
              </div>
              <div>
                <Label htmlFor="edit-email">Email</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={editingUser.email}
                  onChange={(e) => setEditingUser((prev) => (prev ? { ...prev, email: e.target.value } : null))}
                />
              </div>
              <div>
                <Label htmlFor="edit-role">Rôle</Label>
                <Select
                  value={editingUser.role}
                  onValueChange={(value: any) => setEditingUser((prev) => (prev ? { ...prev, role: value } : null))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="STANDARD">Utilisateur Standard</SelectItem>
                    <SelectItem value="JURIDIQUE">Juriste</SelectItem>
                    <SelectItem value="ADMIN">Administrateur</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-statut">Statut</Label>
                <Select
                  value={editingUser.statut}
                  onValueChange={(value: any) => setEditingUser((prev) => (prev ? { ...prev, statut: value } : null))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Actif">Actif</SelectItem>
                    <SelectItem value="Inactif">Inactif</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setEditingUser(null)}>
                  Annuler
                </Button>
                <Button onClick={handleUpdateUser}>Sauvegarder</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
