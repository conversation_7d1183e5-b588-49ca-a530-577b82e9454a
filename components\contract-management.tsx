"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { FileText, Plus, Edit, Trash2, Search, Eye, Download, Calendar, MapPin } from "lucide-react"

interface Contract {
  id: string
  numero: string
  type: "LOCATION" | "ACHAT" | "SERVITUDE" | "LITIGE"
  statut: "ACTIF" | "EXPIRE" | "EN_RENOUVELLEMENT"
  parcelle: string
  proprietaire: string
  dateSignature: string
  echeance: string
  montant?: string
  clauses: string
  fichierPath?: string
}

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface ContractManagementProps {
  user: User
}

export function ContractManagement({ user }: ContractManagementProps) {
  const [contracts, setContracts] = useState<Contract[]>([
    {
      id: "1",
      numero: "CONT-2024-001",
      type: "LOCATION",
      statut: "ACTIF",
      parcelle: "TF-123456 (P-2024-001)",
      proprietaire: "Ahmed Benali",
      dateSignature: "2024-01-15",
      echeance: "2025-01-15",
      montant: "50,000 MAD/mois",
      clauses: "Contrat de location pour exploitation pétrolière. Durée 12 mois renouvelable.",
      fichierPath: "/documents/contrat_location_2024_001.pdf"
    },
    {
      id: "2",
      numero: "CONT-2024-002",
      type: "ACHAT",
      statut: "ACTIF",
      parcelle: "TF-789012 (P-2024-002)",
      proprietaire: "Fatima Alaoui",
      dateSignature: "2024-02-10",
      echeance: "2024-12-31",
      montant: "2,500,000 MAD",
      clauses: "Achat définitif de terrain pour installation d'équipements. Transfert de propriété complet.",
      fichierPath: "/documents/contrat_achat_2024_002.pdf"
    },
    {
      id: "3",
      numero: "CONT-2024-003",
      type: "SERVITUDE",
      statut: "ACTIF",
      parcelle: "TF-345678 (P-2024-003)",
      proprietaire: "Omar Tazi",
      dateSignature: "2024-03-05",
      echeance: "2029-03-05",
      montant: "15,000 MAD/an",
      clauses: "Droit de passage pour pipeline. Servitude de 5 ans avec compensation annuelle.",
      fichierPath: "/documents/contrat_servitude_2024_003.pdf"
    },
    {
      id: "4",
      numero: "CONT-2023-015",
      type: "LOCATION",
      statut: "EXPIRE",
      parcelle: "TF-456789 (P-2023-015)",
      proprietaire: "Youssef Bennani",
      dateSignature: "2023-06-01",
      echeance: "2024-06-01",
      montant: "35,000 MAD/mois",
      clauses: "Contrat de location expiré. Négociations en cours pour renouvellement.",
      fichierPath: "/documents/contrat_location_2023_015.pdf"
    },
    {
      id: "5",
      numero: "CONT-2024-004",
      type: "LITIGE",
      statut: "EN_RENOUVELLEMENT",
      parcelle: "TF-567890 (P-2024-004)",
      proprietaire: "Aicha Mansouri",
      dateSignature: "2024-01-20",
      echeance: "2024-07-20",
      montant: "Compensation à déterminer",
      clauses: "Litige en cours concernant les droits d'exploitation. Médiation en cours.",
      fichierPath: "/documents/contrat_litige_2024_004.pdf"
    },
    {
      id: "6",
      numero: "CONT-2024-005",
      type: "ACHAT",
      statut: "ACTIF",
      parcelle: "TF-678901 (P-2024-005)",
      proprietaire: "Hassan Idrissi",
      dateSignature: "2024-04-12",
      echeance: "2024-10-12",
      montant: "1,800,000 MAD",
      clauses: "Achat de terrain pour extension des installations. Paiement échelonné sur 6 mois.",
      fichierPath: "/documents/contrat_achat_2024_005.pdf"
    }
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("ALL")
  const [typeFilter, setTypeFilter] = useState<string>("ALL")
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newContract, setNewContract] = useState({
    numero: "",
    type: "LOCATION" as Contract["type"],
    parcelle: "",
    proprietaire: "",
    dateSignature: "",
    echeance: "",
    montant: "",
    clauses: ""
  })

  const filteredContracts = contracts.filter((contract) => {
    const matchesSearch = 
      contract.numero.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.proprietaire.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contract.parcelle.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === "ALL" || contract.statut === statusFilter
    const matchesType = typeFilter === "ALL" || contract.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const getStatusBadgeVariant = (status: Contract["statut"]) => {
    switch (status) {
      case "ACTIF":
        return "default"
      case "EXPIRE":
        return "destructive"
      case "EN_RENOUVELLEMENT":
        return "secondary"
      default:
        return "secondary"
    }
  }

  const getTypeBadgeVariant = (type: Contract["type"]) => {
    switch (type) {
      case "LOCATION":
        return "default"
      case "ACHAT":
        return "secondary"
      case "SERVITUDE":
        return "outline"
      case "LITIGE":
        return "destructive"
      default:
        return "secondary"
    }
  }

  const getTypeLabel = (type: Contract["type"]) => {
    switch (type) {
      case "LOCATION":
        return "Location"
      case "ACHAT":
        return "Achat"
      case "SERVITUDE":
        return "Servitude"
      case "LITIGE":
        return "Litige"
      default:
        return type
    }
  }

  const getStatusLabel = (status: Contract["statut"]) => {
    switch (status) {
      case "ACTIF":
        return "Actif"
      case "EXPIRE":
        return "Expiré"
      case "EN_RENOUVELLEMENT":
        return "En Renouvellement"
      default:
        return status
    }
  }

  const handleCreateContract = () => {
    const contract: Contract = {
      id: Math.random().toString(36).substring(2, 9),
      numero: newContract.numero || `CONT-${new Date().getFullYear()}-${String(contracts.length + 1).padStart(3, '0')}`,
      type: newContract.type,
      statut: "ACTIF",
      parcelle: newContract.parcelle,
      proprietaire: newContract.proprietaire,
      dateSignature: newContract.dateSignature,
      echeance: newContract.echeance,
      montant: newContract.montant,
      clauses: newContract.clauses,
    }
    setContracts((prev) => [...prev, contract])
    setNewContract({
      numero: "",
      type: "LOCATION",
      parcelle: "",
      proprietaire: "",
      dateSignature: "",
      echeance: "",
      montant: "",
      clauses: ""
    })
    setIsCreateDialogOpen(false)
  }

  const handleDeleteContract = (contractId: string) => {
    setContracts((prev) => prev.filter((c) => c.id !== contractId))
  }

  const handleViewContract = (contract: Contract) => {
    setSelectedContract(contract)
  }

  const canModifyContracts = user.role === "ADMIN" || user.role === "JURIDIQUE"

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Gestion des Contrats</span>
          </CardTitle>
          <CardDescription>
            Gérez tous les contrats fonciers et juridiques - {filteredContracts.length} contrat(s) affiché(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Rechercher par numéro, propriétaire ou parcelle..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Tous les statuts</SelectItem>
                <SelectItem value="ACTIF">Actif</SelectItem>
                <SelectItem value="EXPIRE">Expiré</SelectItem>
                <SelectItem value="EN_RENOUVELLEMENT">En Renouvellement</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Tous les types</SelectItem>
                <SelectItem value="LOCATION">Location</SelectItem>
                <SelectItem value="ACHAT">Achat</SelectItem>
                <SelectItem value="SERVITUDE">Servitude</SelectItem>
                <SelectItem value="LITIGE">Litige</SelectItem>
              </SelectContent>
            </Select>
            {canModifyContracts && (
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Nouveau Contrat
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Créer un Nouveau Contrat</DialogTitle>
                    <DialogDescription>Saisissez les informations du nouveau contrat</DialogDescription>
                  </DialogHeader>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="numero">Numéro de Contrat</Label>
                      <Input
                        id="numero"
                        placeholder="CONT-2024-XXX"
                        value={newContract.numero}
                        onChange={(e) => setNewContract((prev) => ({ ...prev, numero: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="type">Type de Contrat</Label>
                      <Select value={newContract.type} onValueChange={(value: Contract["type"]) => setNewContract((prev) => ({ ...prev, type: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="LOCATION">Location</SelectItem>
                          <SelectItem value="ACHAT">Achat</SelectItem>
                          <SelectItem value="SERVITUDE">Servitude</SelectItem>
                          <SelectItem value="LITIGE">Litige</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="parcelle">Parcelle</Label>
                      <Input
                        id="parcelle"
                        placeholder="TF-XXXXXX (P-2024-XXX)"
                        value={newContract.parcelle}
                        onChange={(e) => setNewContract((prev) => ({ ...prev, parcelle: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="proprietaire">Propriétaire</Label>
                      <Input
                        id="proprietaire"
                        placeholder="Nom du propriétaire"
                        value={newContract.proprietaire}
                        onChange={(e) => setNewContract((prev) => ({ ...prev, proprietaire: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="dateSignature">Date de Signature</Label>
                      <Input
                        id="dateSignature"
                        type="date"
                        value={newContract.dateSignature}
                        onChange={(e) => setNewContract((prev) => ({ ...prev, dateSignature: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="echeance">Date d'Échéance</Label>
                      <Input
                        id="echeance"
                        type="date"
                        value={newContract.echeance}
                        onChange={(e) => setNewContract((prev) => ({ ...prev, echeance: e.target.value }))}
                      />
                    </div>
                    <div className="col-span-2">
                      <Label htmlFor="montant">Montant</Label>
                      <Input
                        id="montant"
                        placeholder="Ex: 50,000 MAD/mois"
                        value={newContract.montant}
                        onChange={(e) => setNewContract((prev) => ({ ...prev, montant: e.target.value }))}
                      />
                    </div>
                    <div className="col-span-2">
                      <Label htmlFor="clauses">Clauses Principales</Label>
                      <Textarea
                        id="clauses"
                        placeholder="Décrivez les clauses principales du contrat..."
                        value={newContract.clauses}
                        onChange={(e) => setNewContract((prev) => ({ ...prev, clauses: e.target.value }))}
                        rows={3}
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2 mt-4">
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Annuler
                    </Button>
                    <Button onClick={handleCreateContract}>
                      Créer le Contrat
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>

          {/* Contracts Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Numéro</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Parcelle</TableHead>
                  <TableHead>Propriétaire</TableHead>
                  <TableHead>Date Signature</TableHead>
                  <TableHead>Échéance</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContracts.map((contract) => (
                  <TableRow key={contract.id}>
                    <TableCell className="font-medium">{contract.numero}</TableCell>
                    <TableCell>
                      <Badge variant={getTypeBadgeVariant(contract.type)}>
                        {getTypeLabel(contract.type)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-3 w-3 text-gray-400" />
                        <span className="text-sm">{contract.parcelle}</span>
                      </div>
                    </TableCell>
                    <TableCell>{contract.proprietaire}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className="text-sm">{contract.dateSignature}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className="text-sm">{contract.echeance}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(contract.statut)}>
                        {getStatusLabel(contract.statut)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => handleViewContract(contract)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        {contract.fichierPath && (
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                        {canModifyContracts && (
                          <>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteContract(contract.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredContracts.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Aucun contrat trouvé avec les critères de recherche</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Contract Details Dialog */}
      <Dialog open={!!selectedContract} onOpenChange={() => setSelectedContract(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Détails du Contrat {selectedContract?.numero}</span>
            </DialogTitle>
            <DialogDescription>Informations complètes du contrat</DialogDescription>
          </DialogHeader>
          {selectedContract && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Type de Contrat</Label>
                    <div className="mt-1">
                      <Badge variant={getTypeBadgeVariant(selectedContract.type)}>
                        {getTypeLabel(selectedContract.type)}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Statut</Label>
                    <div className="mt-1">
                      <Badge variant={getStatusBadgeVariant(selectedContract.statut)}>
                        {getStatusLabel(selectedContract.statut)}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Parcelle</Label>
                    <p className="mt-1 flex items-center space-x-1">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span>{selectedContract.parcelle}</span>
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Propriétaire</Label>
                    <p className="mt-1">{selectedContract.proprietaire}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Date de Signature</Label>
                    <p className="mt-1 flex items-center space-x-1">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>{selectedContract.dateSignature}</span>
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Date d'Échéance</Label>
                    <p className="mt-1 flex items-center space-x-1">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>{selectedContract.echeance}</span>
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Montant</Label>
                    <p className="mt-1 font-semibold text-green-600">{selectedContract.montant}</p>
                  </div>
                  {selectedContract.fichierPath && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Document</Label>
                      <div className="mt-1">
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Télécharger PDF
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Clauses Principales</Label>
                <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700">{selectedContract.clauses}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
