"use client"

import React from "react"

interface PerencoLogoProps {
  className?: string
  size?: "sm" | "md" | "lg"
}

export function PerencoLogo({ className = "", size = "md" }: PerencoLogoProps) {
  const sizeClasses = {
    sm: "h-8 w-auto",
    md: "h-10 w-auto", 
    lg: "h-12 w-auto"
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Perenco Logo SVG */}
      <svg 
        className={sizeClasses[size]} 
        viewBox="0 0 200 60" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background rectangle */}
        <rect width="200" height="60" rx="4" fill="#1e40af" />
        
        {/* Perenco text */}
        <text 
          x="20" 
          y="38" 
          fill="white" 
          fontSize="24" 
          fontWeight="bold" 
          fontFamily="Arial, sans-serif"
        >
          PERENCO
        </text>
        
        {/* Oil drop icon */}
        <g transform="translate(160, 15)">
          <ellipse cx="15" cy="20" rx="8" ry="12" fill="#fbbf24" />
          <ellipse cx="15" cy="18" rx="6" ry="9" fill="#f59e0b" />
          <circle cx="15" cy="12" r="3" fill="#fbbf24" opacity="0.8" />
        </g>
      </svg>
      
      {/* Company tagline */}
      {size === "lg" && (
        <div className="text-sm text-gray-600">
          <div className="font-medium">Oil & Gas Operations</div>
          <div className="text-xs">Tunisia Division</div>
        </div>
      )}
    </div>
  )
}

// Alternative text-based logo for better compatibility
export function PerencoTextLogo({ className = "", size = "md" }: PerencoLogoProps) {
  const sizeClasses = {
    sm: "text-lg",
    md: "text-xl", 
    lg: "text-2xl"
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Oil drop emoji/icon */}
      <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full">
        <span className="text-yellow-400 text-sm">🛢️</span>
      </div>
      
      {/* Company name */}
      <div className="flex flex-col">
        <span className={`font-bold text-blue-600 ${sizeClasses[size]}`}>
          PERENCO
        </span>
        {size === "lg" && (
          <span className="text-xs text-gray-500 -mt-1">
            Tunisia Operations
          </span>
        )}
      </div>
    </div>
  )
}

// Professional logo with better styling
export function PerencoProfessionalLogo({ className = "", size = "md" }: PerencoLogoProps) {
  const containerClasses = {
    sm: "h-8",
    md: "h-10",
    lg: "h-12"
  }

  const textClasses = {
    sm: "text-base",
    md: "text-lg",
    lg: "text-xl"
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Logo container with gradient background */}
      <div className={`${containerClasses[size]} px-4 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg flex items-center shadow-md`}>
        <div className="flex items-center space-x-2">
          {/* Stylized P letter */}
          <div className="relative">
            <div className={`${textClasses[size]} font-bold text-white`}>P</div>
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full"></div>
          </div>
          
          {/* Company name */}
          <span className={`${textClasses[size]} font-bold text-white tracking-wide`}>
            ERENCO
          </span>
        </div>
      </div>
      
      {/* Tagline for larger sizes */}
      {size === "lg" && (
        <div className="text-sm text-gray-600">
          <div className="font-medium">Exploration & Production</div>
          <div className="text-xs text-gray-500">Southern Tunisia</div>
        </div>
      )}
    </div>
  )
}

// Simple and clean logo version
export function PerencoSimpleLogo({ className = "", size = "md" }: PerencoLogoProps) {
  const textClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl"
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Simple icon */}
      <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
        <div className="w-4 h-4 bg-yellow-400 rounded-full"></div>
      </div>
      
      {/* Text */}
      <span className={`font-bold text-blue-600 ${textClasses[size]} tracking-wide`}>
        PERENCO
      </span>
      
      {size !== "sm" && (
        <span className="text-xs text-gray-500 font-medium">
          TUNISIA
        </span>
      )}
    </div>
  )
}
