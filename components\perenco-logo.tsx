"use client"

import React from "react"
import Image from "next/image"

interface PerencoLogoProps {
  className?: string
  size?: "sm" | "md" | "lg"
}

export function PerencoLogo({ className = "", size = "md" }: PerencoLogoProps) {
  const sizeClasses = {
    sm: "h-8 w-auto",
    md: "h-10 w-auto",
    lg: "h-12 w-auto"
  }

  const heightValues = {
    sm: 32,
    md: 40,
    lg: 48
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Perenco Logo Image */}
      <Image
        src="/logo_perenco.png"
        alt="PERENCO Logo"
        height={heightValues[size]}
        width={heightValues[size] * 3} // Assuming logo is wider than tall
        className={`${sizeClasses[size]} object-contain`}
        priority
      />

      {/* Company tagline */}
      {size === "lg" && (
        <div className="text-sm text-gray-600">
          <div className="font-medium">Oil & Gas Operations</div>
          <div className="text-xs">Tunisia Division</div>
        </div>
      )}
    </div>
  )
}

// Alternative text-based logo for better compatibility
export function PerencoTextLogo({ className = "", size = "md" }: PerencoLogoProps) {
  const sizeClasses = {
    sm: "text-lg",
    md: "text-xl", 
    lg: "text-2xl"
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Oil drop emoji/icon */}
      <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full">
        <span className="text-yellow-400 text-sm">🛢️</span>
      </div>
      
      {/* Company name */}
      <div className="flex flex-col">
        <span className={`font-bold text-blue-600 ${sizeClasses[size]}`}>
          PERENCO
        </span>
        {size === "lg" && (
          <span className="text-xs text-gray-500 -mt-1">
            Tunisia Operations
          </span>
        )}
      </div>
    </div>
  )
}

// Professional logo with real image
export function PerencoProfessionalLogo({ className = "", size = "md" }: PerencoLogoProps) {
  const heightValues = {
    sm: 32,
    md: 40,
    lg: 48
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Real Perenco Logo */}
      <div className="flex items-center">
        <Image
          src="/logo_perenco.png"
          alt="PERENCO Logo"
          height={heightValues[size]}
          width={heightValues[size] * 3} // Assuming logo is wider than tall
          className="object-contain"
          priority
        />
      </div>

      {/* Tagline for larger sizes */}
      {size === "lg" && (
        <div className="text-sm text-gray-600">
          <div className="font-medium">Exploration & Production</div>
          <div className="text-xs text-gray-500">Southern Tunisia</div>
        </div>
      )}
    </div>
  )
}

// Simple and clean logo version
export function PerencoSimpleLogo({ className = "", size = "md" }: PerencoLogoProps) {
  const textClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl"
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Simple icon */}
      <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
        <div className="w-4 h-4 bg-yellow-400 rounded-full"></div>
      </div>
      
      {/* Text */}
      <span className={`font-bold text-blue-600 ${textClasses[size]} tracking-wide`}>
        PERENCO
      </span>
      
      {size !== "sm" && (
        <span className="text-xs text-gray-500 font-medium">
          TUNISIA
        </span>
      )}
    </div>
  )
}
