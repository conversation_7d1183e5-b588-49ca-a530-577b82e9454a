"use client"

import Image from "next/image"
import { Map } from "lucide-react"
import { cn } from "@/lib/utils"

interface LogoProps {
  variant?: "header" | "login"
  className?: string
}

export function Logo({ variant = "header", className }: LogoProps) {
  const isLogin = variant === "login"

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      {/* Map Icon */}
      <div className={cn(
        "flex-shrink-0",
        isLogin ? "p-2 bg-blue-100 rounded-full" : ""
      )}>
        <Map className={cn(
          "text-blue-600",
          isLogin ? "h-6 w-6" : "h-8 w-8"
        )} />
      </div>
    </div>
  )
}

// Separate Perenco Logo component for upper right positioning
export function PerencoLogo({ variant = "header" }: { variant?: "header" | "login" }) {
  const isLogin = variant === "login"

  return (
    <div className="absolute top-4 right-4 z-10">
      <Image
        src="/logo_perenco.png"
        alt="Perenco Logo"
        width={isLogin ? 120 : 80}
        height={isLogin ? 120 : 80}
        className="object-contain"
      />
    </div>
  )
}
