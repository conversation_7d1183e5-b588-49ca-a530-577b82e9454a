"use client"

import Image from "next/image"
import { Map } from "lucide-react"
import { cn } from "@/lib/utils"

interface LogoProps {
  variant?: "header" | "login"
  className?: string
}

export function Logo({ variant = "header", className }: LogoProps) {
  const isLogin = variant === "login"
  
  return (
    <div className={cn("flex items-center space-x-3", className)}>
      {/* Perenco Logo */}
      <div className="flex-shrink-0">
        <Image
          src="/logo_perenco.png"
          alt="Perenco Logo"
          width={isLogin ? 40 : 32}
          height={isLogin ? 40 : 32}
          className="object-contain"
        />
      </div>
      
      {/* Map Icon */}
      <div className={cn(
        "flex-shrink-0",
        isLogin ? "p-2 bg-blue-100 rounded-full" : ""
      )}>
        <Map className={cn(
          "text-blue-600",
          isLogin ? "h-6 w-6" : "h-8 w-8"
        )} />
      </div>
    </div>
  )
}
