export interface DocumentNotification {
  id: string
  type: "EXPIRING" | "EXPIRED" | "RENEWAL_DUE"
  title: string
  message: string
  documentId: string
  documentName: string
  documentType: string
  expirationDate: string
  daysRemaining: number
  priority: "HIGH" | "MEDIUM" | "LOW"
  isRead: boolean
  createdAt: string
  parcelNumber?: string
  owner?: string
}

export interface NotificationStats {
  total: number
  unread: number
  expired: number
  expiringSoon: number
  renewalDue: number
}

export class NotificationService {
  private static instance: NotificationService
  private notifications: DocumentNotification[] = []

  private constructor() {
    this.initializeNotifications()
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  private initializeNotifications(): void {
    // Sample notifications for demonstration
    this.notifications = [
      {
        id: "notif-001",
        type: "EXPIRED",
        title: "Document Expiré",
        message: "Le contrat de location TF-123456 a expiré le 15/03/2024",
        documentId: "CONT-2024-001",
        documentName: "Contrat_Location_TF123456.pdf",
        documentType: "Contrat",
        expirationDate: "2024-03-15",
        daysRemaining: -5,
        priority: "HIGH",
        isRead: false,
        createdAt: "2024-03-20T10:00:00Z",
        parcelNumber: "TF-123456",
        owner: "Ahmed Benali"
      },
      {
        id: "notif-002",
        type: "EXPIRING",
        title: "Document Expire Bientôt",
        message: "Le permis d'exploitation TF-789012 expire dans 15 jours",
        documentId: "PERM-2024-005",
        documentName: "Permis_Exploitation_TF789012.pdf",
        documentType: "Permis",
        expirationDate: "2024-04-10",
        daysRemaining: 15,
        priority: "MEDIUM",
        isRead: false,
        createdAt: "2024-03-26T14:30:00Z",
        parcelNumber: "TF-789012",
        owner: "Fatima Alaoui"
      },
      {
        id: "notif-003",
        type: "RENEWAL_DUE",
        title: "Renouvellement Requis",
        message: "L'étude géologique TF-345678 nécessite un renouvellement dans 30 jours",
        documentId: "ETU-2024-012",
        documentName: "Etude_Geologique_TF345678.pdf",
        documentType: "Étude",
        expirationDate: "2024-04-25",
        daysRemaining: 30,
        priority: "LOW",
        isRead: true,
        createdAt: "2024-03-25T09:15:00Z",
        parcelNumber: "TF-345678",
        owner: "Omar Tazi"
      },
      {
        id: "notif-004",
        type: "EXPIRING",
        title: "Autorisation Expire Bientôt",
        message: "L'autorisation de forage TF-456789 expire dans 7 jours",
        documentId: "AUTH-2024-008",
        documentName: "Autorisation_Forage_TF456789.pdf",
        documentType: "Autorisation",
        expirationDate: "2024-04-02",
        daysRemaining: 7,
        priority: "HIGH",
        isRead: false,
        createdAt: "2024-03-26T16:45:00Z",
        parcelNumber: "TF-456789",
        owner: "Youssef Bennani"
      },
      {
        id: "notif-005",
        type: "EXPIRING",
        title: "Plan Cadastral à Renouveler",
        message: "Le plan cadastral TF-567890 expire dans 45 jours",
        documentId: "PLAN-2024-003",
        documentName: "Plan_Cadastral_TF567890.pdf",
        documentType: "Plan",
        expirationDate: "2024-05-10",
        daysRemaining: 45,
        priority: "LOW",
        isRead: false,
        createdAt: "2024-03-25T11:20:00Z",
        parcelNumber: "TF-567890",
        owner: "Aicha Mansouri"
      }
    ]
  }

  public getAllNotifications(): DocumentNotification[] {
    return this.notifications.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
  }

  public getUnreadNotifications(): DocumentNotification[] {
    return this.notifications.filter(n => !n.isRead)
  }

  public getNotificationsByPriority(priority: "HIGH" | "MEDIUM" | "LOW"): DocumentNotification[] {
    return this.notifications.filter(n => n.priority === priority)
  }

  public getNotificationsByType(type: "EXPIRING" | "EXPIRED" | "RENEWAL_DUE"): DocumentNotification[] {
    return this.notifications.filter(n => n.type === type)
  }

  public markAsRead(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId)
    if (notification) {
      notification.isRead = true
    }
  }

  public markAllAsRead(): void {
    this.notifications.forEach(n => n.isRead = true)
  }

  public deleteNotification(notificationId: string): void {
    this.notifications = this.notifications.filter(n => n.id !== notificationId)
  }

  public getNotificationStats(): NotificationStats {
    const total = this.notifications.length
    const unread = this.notifications.filter(n => !n.isRead).length
    const expired = this.notifications.filter(n => n.type === "EXPIRED").length
    const expiringSoon = this.notifications.filter(n => n.type === "EXPIRING" && n.daysRemaining <= 30).length
    const renewalDue = this.notifications.filter(n => n.type === "RENEWAL_DUE").length

    return {
      total,
      unread,
      expired,
      expiringSoon,
      renewalDue
    }
  }

  public addDocumentExpirationNotification(
    documentId: string,
    documentName: string,
    documentType: string,
    expirationDate: string,
    parcelNumber?: string,
    owner?: string
  ): void {
    const expDate = new Date(expirationDate)
    const today = new Date()
    const diffTime = expDate.getTime() - today.getTime()
    const daysRemaining = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    let type: "EXPIRING" | "EXPIRED" | "RENEWAL_DUE"
    let priority: "HIGH" | "MEDIUM" | "LOW"
    let title: string
    let message: string

    if (daysRemaining < 0) {
      type = "EXPIRED"
      priority = "HIGH"
      title = "Document Expiré"
      message = `Le ${documentType.toLowerCase()} ${parcelNumber || documentName} a expiré le ${expDate.toLocaleDateString('fr-FR')}`
    } else if (daysRemaining <= 7) {
      type = "EXPIRING"
      priority = "HIGH"
      title = "Document Expire Très Bientôt"
      message = `Le ${documentType.toLowerCase()} ${parcelNumber || documentName} expire dans ${daysRemaining} jour(s)`
    } else if (daysRemaining <= 30) {
      type = "EXPIRING"
      priority = "MEDIUM"
      title = "Document Expire Bientôt"
      message = `Le ${documentType.toLowerCase()} ${parcelNumber || documentName} expire dans ${daysRemaining} jours`
    } else if (daysRemaining <= 90) {
      type = "RENEWAL_DUE"
      priority = "LOW"
      title = "Renouvellement à Prévoir"
      message = `Le ${documentType.toLowerCase()} ${parcelNumber || documentName} expire dans ${daysRemaining} jours`
    } else {
      return // No notification needed for documents expiring in more than 90 days
    }

    const notification: DocumentNotification = {
      id: `notif-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type,
      title,
      message,
      documentId,
      documentName,
      documentType,
      expirationDate,
      daysRemaining,
      priority,
      isRead: false,
      createdAt: new Date().toISOString(),
      parcelNumber,
      owner
    }

    // Check if notification already exists for this document
    const existingIndex = this.notifications.findIndex(n => n.documentId === documentId)
    if (existingIndex >= 0) {
      this.notifications[existingIndex] = notification
    } else {
      this.notifications.push(notification)
    }
  }

  public checkDocumentExpirations(documents: Array<{
    id: string
    name: string
    type: string
    expirationDate?: string
    parcelNumber?: string
    owner?: string
  }>): void {
    documents.forEach(doc => {
      if (doc.expirationDate) {
        this.addDocumentExpirationNotification(
          doc.id,
          doc.name,
          doc.type,
          doc.expirationDate,
          doc.parcelNumber,
          doc.owner
        )
      }
    })
  }
}

export const notificationService = NotificationService.getInstance()
