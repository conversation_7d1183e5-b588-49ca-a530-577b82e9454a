"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { FileText, Plus, Eye, Download, Search, Calendar, User, MapPin, AlertCircle } from "lucide-react"

interface Report {
  id: string
  titre: string
  type: "MENSUEL" | "TRIMESTRIEL" | "ANNUEL" | "INCIDENT" | "CONFORMITE" | "ENVIRONNEMENTAL"
  statut: "BRO<PERSON>LLON" | "SOUMIS" | "EN_REVISION" | "APPROUVE" | "REJETE"
  auteur: string
  dateCreation: string
  dateModification: string
  parcelles: string[]
  periode: {
    debut: string
    fin: string
  }
  contenu: string
  commentaires?: string
  fichierPath?: string
}

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface ReportsManagementProps {
  user: User
}

export function ReportsManagement({ user }: ReportsManagementProps) {
  const [reports, setReports] = useState<Report[]>([
    {
      id: "RPT-2024-001",
      titre: "Rapport Mensuel - Janvier 2024",
      type: "MENSUEL",
      statut: "APPROUVE",
      auteur: "Jihen KHEBOU",
      dateCreation: "2024-01-31",
      dateModification: "2024-02-02",
      parcelles: ["TF-123456", "TF-789012"],
      periode: { debut: "2024-01-01", fin: "2024-01-31" },
      contenu: "Rapport mensuel des activités d'exploitation sur les parcelles TF-123456 et TF-789012. Production normale, aucun incident signalé.",
      commentaires: "Rapport approuvé par le service juridique. Conforme aux exigences réglementaires.",
      fichierPath: "/reports/rapport_mensuel_janvier_2024.pdf"
    },
    {
      id: "RPT-2024-002",
      titre: "Rapport d'Incident - Fuite Pipeline",
      type: "INCIDENT",
      statut: "EN_REVISION",
      auteur: "Adam SMIDA",
      dateCreation: "2024-02-15",
      dateModification: "2024-02-16",
      parcelles: ["TF-789012"],
      periode: { debut: "2024-02-15", fin: "2024-02-15" },
      contenu: "Incident mineur détecté sur le pipeline de la parcelle TF-789012. Fuite colmatée immédiatement. Aucun impact environnemental.",
      commentaires: "En attente de validation par le service juridique.",
      fichierPath: "/reports/rapport_incident_pipeline_2024.pdf"
    },
    {
      id: "RPT-2024-003",
      titre: "Rapport de Conformité Environnementale",
      type: "ENVIRONNEMENTAL",
      statut: "SOUMIS",
      auteur: "Jihen KHEBOU",
      dateCreation: "2024-03-01",
      dateModification: "2024-03-01",
      parcelles: ["TF-123456", "TF-345678", "TF-456789"],
      periode: { debut: "2024-01-01", fin: "2024-02-29" },
      contenu: "Évaluation de l'impact environnemental des activités sur les parcelles d'exploitation. Conformité aux normes tunisiennes.",
      fichierPath: "/reports/rapport_environnemental_q1_2024.pdf"
    },
    {
      id: "RPT-2024-004",
      titre: "Rapport Trimestriel Q1 2024",
      type: "TRIMESTRIEL",
      statut: "BROUILLON",
      auteur: "Adam SMIDA",
      dateCreation: "2024-03-25",
      dateModification: "2024-03-28",
      parcelles: ["TF-123456", "TF-789012", "TF-345678", "TF-456789", "TF-567890"],
      periode: { debut: "2024-01-01", fin: "2024-03-31" },
      contenu: "Synthèse des activités du premier trimestre 2024. Analyse de la production, incidents, conformité réglementaire.",
    },
    {
      id: "RPT-2024-005",
      titre: "Rapport de Conformité Réglementaire",
      type: "CONFORMITE",
      statut: "REJETE",
      auteur: "Jihen KHEBOU",
      dateCreation: "2024-02-20",
      dateModification: "2024-02-25",
      parcelles: ["TF-567890"],
      periode: { debut: "2024-02-01", fin: "2024-02-29" },
      contenu: "Vérification de la conformité réglementaire pour la parcelle TF-567890. Quelques non-conformités identifiées.",
      commentaires: "Rapport rejeté - Corrections nécessaires avant resoumission. Voir détails en annexe.",
    }
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("ALL")
  const [typeFilter, setTypeFilter] = useState<string>("ALL")
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newReport, setNewReport] = useState({
    titre: "",
    type: "MENSUEL" as Report["type"],
    parcelles: "",
    periodeDebut: "",
    periodeFin: "",
    contenu: ""
  })

  // Filter reports based on user role
  const visibleReports = reports.filter((report) => {
    // Standard users can only see their own reports
    if (user.role === "STANDARD") {
      return report.auteur === user.nom
    }
    // Admin and Juridique can see all reports
    return true
  })

  const filteredReports = visibleReports.filter((report) => {
    const matchesSearch = 
      report.titre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.auteur.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.parcelles.some(p => p.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesStatus = statusFilter === "ALL" || report.statut === statusFilter
    const matchesType = typeFilter === "ALL" || report.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const getStatusBadgeVariant = (status: Report["statut"]) => {
    switch (status) {
      case "APPROUVE":
        return "default"
      case "EN_REVISION":
        return "secondary"
      case "SOUMIS":
        return "outline"
      case "BROUILLON":
        return "secondary"
      case "REJETE":
        return "destructive"
      default:
        return "secondary"
    }
  }

  const getTypeBadgeVariant = (type: Report["type"]) => {
    switch (type) {
      case "MENSUEL":
        return "default"
      case "TRIMESTRIEL":
        return "secondary"
      case "ANNUEL":
        return "outline"
      case "INCIDENT":
        return "destructive"
      case "CONFORMITE":
        return "default"
      case "ENVIRONNEMENTAL":
        return "secondary"
      default:
        return "secondary"
    }
  }

  const getTypeLabel = (type: Report["type"]) => {
    switch (type) {
      case "MENSUEL":
        return "Mensuel"
      case "TRIMESTRIEL":
        return "Trimestriel"
      case "ANNUEL":
        return "Annuel"
      case "INCIDENT":
        return "Incident"
      case "CONFORMITE":
        return "Conformité"
      case "ENVIRONNEMENTAL":
        return "Environnemental"
      default:
        return type
    }
  }

  const getStatusLabel = (status: Report["statut"]) => {
    switch (status) {
      case "BROUILLON":
        return "Brouillon"
      case "SOUMIS":
        return "Soumis"
      case "EN_REVISION":
        return "En Révision"
      case "APPROUVE":
        return "Approuvé"
      case "REJETE":
        return "Rejeté"
      default:
        return status
    }
  }

  const handleCreateReport = () => {
    const report: Report = {
      id: `RPT-${new Date().getFullYear()}-${String(reports.length + 1).padStart(3, '0')}`,
      titre: newReport.titre,
      type: newReport.type,
      statut: "BROUILLON",
      auteur: user.nom,
      dateCreation: new Date().toISOString().split('T')[0],
      dateModification: new Date().toISOString().split('T')[0],
      parcelles: newReport.parcelles.split(',').map(p => p.trim()).filter(p => p),
      periode: {
        debut: newReport.periodeDebut,
        fin: newReport.periodeFin
      },
      contenu: newReport.contenu,
    }
    setReports((prev) => [...prev, report])
    setNewReport({
      titre: "",
      type: "MENSUEL",
      parcelles: "",
      periodeDebut: "",
      periodeFin: "",
      contenu: ""
    })
    setIsCreateDialogOpen(false)
  }

  const handleViewReport = (report: Report) => {
    setSelectedReport(report)
  }

  const handleSubmitReport = (reportId: string) => {
    setReports((prev) => prev.map((r) => 
      r.id === reportId ? { ...r, statut: "SOUMIS", dateModification: new Date().toISOString().split('T')[0] } : r
    ))
  }

  const handleApproveReport = (reportId: string) => {
    if (user.role === "JURIDIQUE" || user.role === "ADMIN") {
      setReports((prev) => prev.map((r) => 
        r.id === reportId ? { ...r, statut: "APPROUVE", dateModification: new Date().toISOString().split('T')[0] } : r
      ))
    }
  }

  const handleRejectReport = (reportId: string) => {
    if (user.role === "JURIDIQUE" || user.role === "ADMIN") {
      setReports((prev) => prev.map((r) => 
        r.id === reportId ? { ...r, statut: "REJETE", dateModification: new Date().toISOString().split('T')[0] } : r
      ))
    }
  }

  const canCreateReports = user.role === "ADMIN" || user.role === "STANDARD"
  const canApproveReports = user.role === "JURIDIQUE" || user.role === "ADMIN"

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Gestion des Rapports</span>
            </div>
            <Badge variant="outline">
              {filteredReports.length} rapport(s) affiché(s)
            </Badge>
          </CardTitle>
          <CardDescription>
            {user.role === "STANDARD" 
              ? "Créez et gérez vos rapports d'activité" 
              : "Consultez et validez les rapports d'activité"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Rechercher par titre, auteur ou parcelle..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Tous les statuts</SelectItem>
                <SelectItem value="BROUILLON">Brouillon</SelectItem>
                <SelectItem value="SOUMIS">Soumis</SelectItem>
                <SelectItem value="EN_REVISION">En Révision</SelectItem>
                <SelectItem value="APPROUVE">Approuvé</SelectItem>
                <SelectItem value="REJETE">Rejeté</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Tous les types</SelectItem>
                <SelectItem value="MENSUEL">Mensuel</SelectItem>
                <SelectItem value="TRIMESTRIEL">Trimestriel</SelectItem>
                <SelectItem value="ANNUEL">Annuel</SelectItem>
                <SelectItem value="INCIDENT">Incident</SelectItem>
                <SelectItem value="CONFORMITE">Conformité</SelectItem>
                <SelectItem value="ENVIRONNEMENTAL">Environnemental</SelectItem>
              </SelectContent>
            </Select>
            {canCreateReports && (
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Nouveau Rapport
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Créer un Nouveau Rapport</DialogTitle>
                    <DialogDescription>Saisissez les informations du rapport</DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="titre">Titre du Rapport</Label>
                      <Input
                        id="titre"
                        placeholder="Ex: Rapport Mensuel - Mars 2024"
                        value={newReport.titre}
                        onChange={(e) => setNewReport((prev) => ({ ...prev, titre: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="type">Type de Rapport</Label>
                      <Select value={newReport.type} onValueChange={(value: Report["type"]) => setNewReport((prev) => ({ ...prev, type: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="MENSUEL">Mensuel</SelectItem>
                          <SelectItem value="TRIMESTRIEL">Trimestriel</SelectItem>
                          <SelectItem value="ANNUEL">Annuel</SelectItem>
                          <SelectItem value="INCIDENT">Incident</SelectItem>
                          <SelectItem value="CONFORMITE">Conformité</SelectItem>
                          <SelectItem value="ENVIRONNEMENTAL">Environnemental</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="parcelles">Parcelles Concernées</Label>
                      <Input
                        id="parcelles"
                        placeholder="TF-123456, TF-789012, ..."
                        value={newReport.parcelles}
                        onChange={(e) => setNewReport((prev) => ({ ...prev, parcelles: e.target.value }))}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="periodeDebut">Période - Début</Label>
                        <Input
                          id="periodeDebut"
                          type="date"
                          value={newReport.periodeDebut}
                          onChange={(e) => setNewReport((prev) => ({ ...prev, periodeDebut: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="periodeFin">Période - Fin</Label>
                        <Input
                          id="periodeFin"
                          type="date"
                          value={newReport.periodeFin}
                          onChange={(e) => setNewReport((prev) => ({ ...prev, periodeFin: e.target.value }))}
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="contenu">Contenu du Rapport</Label>
                      <Textarea
                        id="contenu"
                        placeholder="Décrivez le contenu du rapport..."
                        value={newReport.contenu}
                        onChange={(e) => setNewReport((prev) => ({ ...prev, contenu: e.target.value }))}
                        rows={4}
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2 mt-4">
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Annuler
                    </Button>
                    <Button onClick={handleCreateReport}>
                      Créer le Rapport
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>

          {/* Reports Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Titre</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Auteur</TableHead>
                  <TableHead>Période</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Date Création</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredReports.map((report) => (
                  <TableRow key={report.id}>
                    <TableCell className="font-medium">{report.titre}</TableCell>
                    <TableCell>
                      <Badge variant={getTypeBadgeVariant(report.type)}>
                        {getTypeLabel(report.type)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3 text-gray-400" />
                        <span className="text-sm">{report.auteur}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className="text-sm">{report.periode.debut} - {report.periode.fin}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(report.statut)}>
                        {getStatusLabel(report.statut)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{report.dateCreation}</span>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" onClick={() => handleViewReport(report)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        {report.fichierPath && (
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                        {report.auteur === user.nom && report.statut === "BROUILLON" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSubmitReport(report.id)}
                          >
                            Soumettre
                          </Button>
                        )}
                        {canApproveReports && report.statut === "SOUMIS" && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleApproveReport(report.id)}
                              className="text-green-600 hover:text-green-700"
                            >
                              Approuver
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRejectReport(report.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              Rejeter
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredReports.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Aucun rapport trouvé avec les critères de recherche</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Report Details Dialog */}
      <Dialog open={!!selectedReport} onOpenChange={() => setSelectedReport(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Détails du Rapport - {selectedReport?.id}</span>
            </DialogTitle>
            <DialogDescription>Informations complètes du rapport</DialogDescription>
          </DialogHeader>
          {selectedReport && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Titre</Label>
                    <p className="mt-1 font-medium">{selectedReport.titre}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Type de Rapport</Label>
                    <div className="mt-1">
                      <Badge variant={getTypeBadgeVariant(selectedReport.type)}>
                        {getTypeLabel(selectedReport.type)}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Statut</Label>
                    <div className="mt-1">
                      <Badge variant={getStatusBadgeVariant(selectedReport.statut)}>
                        {getStatusLabel(selectedReport.statut)}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Auteur</Label>
                    <p className="mt-1 flex items-center space-x-1">
                      <User className="h-4 w-4 text-gray-400" />
                      <span>{selectedReport.auteur}</span>
                    </p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Période Couverte</Label>
                    <p className="mt-1 flex items-center space-x-1">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>{selectedReport.periode.debut} - {selectedReport.periode.fin}</span>
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Date de Création</Label>
                    <p className="mt-1">{selectedReport.dateCreation}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Dernière Modification</Label>
                    <p className="mt-1">{selectedReport.dateModification}</p>
                  </div>
                  {selectedReport.fichierPath && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Document</Label>
                      <div className="mt-1">
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Télécharger PDF
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-500">Parcelles Concernées</Label>
                <div className="mt-2 flex flex-wrap gap-2">
                  {selectedReport.parcelles.map((parcelle) => (
                    <Badge key={parcelle} variant="outline" className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span>{parcelle}</span>
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-500">Contenu du Rapport</Label>
                <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700 whitespace-pre-wrap">{selectedReport.contenu}</p>
                </div>
              </div>

              {selectedReport.commentaires && (
                <div>
                  <Label className="text-sm font-medium text-gray-500 flex items-center space-x-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>Commentaires</span>
                  </Label>
                  <div className="mt-2 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-sm text-yellow-800">{selectedReport.commentaires}</p>
                  </div>
                </div>
              )}

              {/* Action buttons for juriste/admin */}
              {canApproveReports && selectedReport.statut === "SOUMIS" && (
                <div className="flex justify-end space-x-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => handleRejectReport(selectedReport.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    Rejeter le Rapport
                  </Button>
                  <Button
                    onClick={() => handleApproveReport(selectedReport.id)}
                    className="text-green-600 hover:text-green-700"
                  >
                    Approuver le Rapport
                  </Button>
                </div>
              )}

              {/* Submit button for author */}
              {selectedReport.auteur === user.nom && selectedReport.statut === "BROUILLON" && (
                <div className="flex justify-end pt-4 border-t">
                  <Button onClick={() => handleSubmitReport(selectedReport.id)}>
                    Soumettre pour Révision
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
