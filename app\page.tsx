"use client"

import { useState, useEffect } from "react"
import { LoginForm } from "@/components/login-form"
import { Dashboard } from "@/components/dashboard"
import { MapViewer } from "@/components/map-viewer"
import { DocumentUpload } from "@/components/document-upload"
import { UserManagement } from "@/components/user-management"
import { ContractManagement } from "@/components/contract-management"
import { ReportsManagement } from "@/components/reports-management"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { LogOut, Map, Upload, Users, FileText } from "lucide-react"
import { Logo, PerencoLogo } from "@/components/ui/logo"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

export default function App() {
  const [user, setUser] = useState<User | null>(null)
  const [activeTab, setActiveTab] = useState("dashboard")

  useEffect(() => {
    // Check for existing session
    const savedUser = localStorage.getItem("user")
    if (savedUser) {
      setUser(JSON.parse(savedUser))
    }
  }, [])

  const handleLogin = (userData: User) => {
    setUser(userData)
    localStorage.setItem("user", JSON.stringify(userData))
  }

  const handleLogout = () => {
    setUser(null)
    localStorage.removeItem("user")
    setActiveTab("dashboard")
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center p-4 relative">
        <PerencoLogo variant="login" />
        <LoginForm onLogin={handleLogin} />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      <PerencoLogo variant="header" />
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Logo variant="header" />
              <h1 className="text-xl font-semibold text-gray-900">Système de Gestion Foncière</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {user.nom} ({user.role})
              </span>
              <Button variant="outline" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Déconnexion
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className={`grid w-full ${
            user.role === "JURIDIQUE" ? "grid-cols-6" :
            user.role === "ADMIN" ? "grid-cols-5" : "grid-cols-4"
          }`}>
            <TabsTrigger value="dashboard">
              <FileText className="h-4 w-4 mr-2" />
              Tableau de Bord
            </TabsTrigger>
            {(user.role === "JURIDIQUE" || user.role === "STANDARD") && (
              <TabsTrigger value="map">
                <Map className="h-4 w-4 mr-2" />
                Carte Interactive
              </TabsTrigger>
            )}
            {(user.role === "ADMIN" || user.role === "JURIDIQUE") && (
              <TabsTrigger value="upload">
                <Upload className="h-4 w-4 mr-2" />
                Upload Documents
              </TabsTrigger>
            )}
            {(user.role === "ADMIN" || user.role === "JURIDIQUE") && (
              <TabsTrigger value="users">
                <Users className="h-4 w-4 mr-2" />
                Gestion Utilisateurs
              </TabsTrigger>
            )}
            <TabsTrigger value="reports">
              <FileText className="h-4 w-4 mr-2" />
              Rapports
            </TabsTrigger>
            <TabsTrigger value="contracts">
              <FileText className="h-4 w-4 mr-2" />
              Contrats
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="mt-6">
            <Dashboard user={user} />
          </TabsContent>

          {(user.role === "JURIDIQUE" || user.role === "STANDARD") && (
            <TabsContent value="map" className="mt-6">
              <MapViewer user={user} />
            </TabsContent>
          )}

          {(user.role === "ADMIN" || user.role === "JURIDIQUE") && (
            <TabsContent value="upload" className="mt-6">
              <DocumentUpload user={user} />
            </TabsContent>
          )}

          {(user.role === "ADMIN" || user.role === "JURIDIQUE") && (
            <TabsContent value="users" className="mt-6">
              <UserManagement />
            </TabsContent>
          )}

          <TabsContent value="reports" className="mt-6">
            <ReportsManagement user={user} />
          </TabsContent>

          <TabsContent value="contracts" className="mt-6">
            <ContractManagement user={user} />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
