"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { 
  Bell, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  Trash2,
  Calendar,
  FileText,
  MapPin,
  User,
  MoreHorizontal
} from "lucide-react"
import { notificationService, DocumentNotification, NotificationStats } from "@/lib/notification-service"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

interface NotificationBellProps {
  user: User
}

export function NotificationBell({ user }: NotificationBellProps) {
  const [notifications, setNotifications] = useState<DocumentNotification[]>([])
  const [stats, setStats] = useState<NotificationStats>({ total: 0, unread: 0, expired: 0, expiringSoon: 0, renewalDue: 0 })
  const [selectedNotification, setSelectedNotification] = useState<DocumentNotification | null>(null)
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    loadNotifications()
  }, [])

  const loadNotifications = () => {
    const allNotifications = notificationService.getAllNotifications()
    const notificationStats = notificationService.getNotificationStats()
    setNotifications(allNotifications.slice(0, 5)) // Show only first 5 in dropdown
    setStats(notificationStats)
  }

  const handleMarkAsRead = (notificationId: string) => {
    notificationService.markAsRead(notificationId)
    loadNotifications()
  }

  const handleMarkAllAsRead = () => {
    notificationService.markAllAsRead()
    loadNotifications()
  }

  const handleDeleteNotification = (notificationId: string) => {
    notificationService.deleteNotification(notificationId)
    loadNotifications()
  }

  const handleViewDetails = (notification: DocumentNotification) => {
    setSelectedNotification(notification)
    setIsOpen(false)
    if (!notification.isRead) {
      handleMarkAsRead(notification.id)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "EXPIRED":
        return <AlertTriangle className="h-3 w-3 text-red-500" />
      case "EXPIRING":
        return <Clock className="h-3 w-3 text-orange-500" />
      case "RENEWAL_DUE":
        return <Calendar className="h-3 w-3 text-blue-500" />
      default:
        return <Bell className="h-3 w-3 text-gray-500" />
    }
  }

  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case "EXPIRED":
        return "destructive"
      case "EXPIRING":
        return "secondary"
      case "RENEWAL_DUE":
        return "outline"
      default:
        return "secondary"
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "EXPIRED":
        return "Expiré"
      case "EXPIRING":
        return "Expire Bientôt"
      case "RENEWAL_DUE":
        return "Renouvellement"
      default:
        return type
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return "Il y a moins d'1h"
    if (diffInHours < 24) return `Il y a ${diffInHours}h`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `Il y a ${diffInDays} jour(s)`
    
    return date.toLocaleDateString('fr-FR')
  }

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-5 w-5" />
            {stats.unread > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {stats.unread > 9 ? "9+" : stats.unread}
              </span>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-96 p-0">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Notifications</h3>
              <div className="flex items-center space-x-2">
                {stats.unread > 0 && (
                  <Button variant="ghost" size="sm" onClick={handleMarkAllAsRead}>
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Tout lire
                  </Button>
                )}
                <Badge variant="outline" className="text-xs">
                  {stats.unread} non lue(s)
                </Badge>
              </div>
            </div>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-2 mt-3">
              <div className="text-center p-2 bg-red-50 rounded">
                <div className="text-lg font-bold text-red-600">{stats.expired}</div>
                <div className="text-xs text-red-600">Expirés</div>
              </div>
              <div className="text-center p-2 bg-orange-50 rounded">
                <div className="text-lg font-bold text-orange-600">{stats.expiringSoon}</div>
                <div className="text-xs text-orange-600">Expire Bientôt</div>
              </div>
              <div className="text-center p-2 bg-blue-50 rounded">
                <div className="text-lg font-bold text-blue-600">{stats.renewalDue}</div>
                <div className="text-xs text-blue-600">Renouvellements</div>
              </div>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {notifications.length > 0 ? (
              <div className="divide-y">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-3 hover:bg-gray-50 cursor-pointer ${
                      !notification.isRead ? "bg-blue-50" : ""
                    }`}
                    onClick={() => handleViewDetails(notification)}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="mt-1">
                        {getTypeIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className={`text-sm font-medium ${!notification.isRead ? "font-semibold" : ""}`}>
                            {notification.title}
                          </h4>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                        <p className="text-xs text-gray-600 mb-1 line-clamp-2">{notification.message}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 text-xs text-gray-500">
                            {notification.parcelNumber && (
                              <span className="flex items-center space-x-1">
                                <MapPin className="h-3 w-3" />
                                <span>{notification.parcelNumber}</span>
                              </span>
                            )}
                            <span>{formatTimeAgo(notification.createdAt)}</span>
                          </div>
                          <Badge variant={getTypeBadgeVariant(notification.type)} className="text-xs">
                            {getTypeLabel(notification.type)}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex space-x-1">
                        {!notification.isRead && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleMarkAsRead(notification.id)
                            }}
                            className="h-6 w-6 p-0"
                          >
                            <CheckCircle className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteNotification(notification.id)
                          }}
                          className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-6 text-center text-gray-500">
                <Bell className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">Aucune notification</p>
              </div>
            )}
          </div>

          {notifications.length > 0 && (
            <div className="p-3 border-t bg-gray-50">
              <Button variant="ghost" size="sm" className="w-full text-xs">
                <MoreHorizontal className="h-3 w-3 mr-1" />
                Voir toutes les notifications ({stats.total})
              </Button>
            </div>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Notification Details Dialog */}
      <Dialog open={!!selectedNotification} onOpenChange={() => setSelectedNotification(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              {selectedNotification && getTypeIcon(selectedNotification.type)}
              <span>Détails de la Notification</span>
            </DialogTitle>
            <DialogDescription>Informations sur l'expiration du document</DialogDescription>
          </DialogHeader>
          {selectedNotification && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-500 mb-2">Document</h4>
                  <div className="space-y-1 text-sm">
                    <p><strong>Nom:</strong> {selectedNotification.documentName}</p>
                    <p><strong>Type:</strong> {selectedNotification.documentType}</p>
                    <p><strong>ID:</strong> {selectedNotification.documentId}</p>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-500 mb-2">Expiration</h4>
                  <div className="space-y-1 text-sm">
                    <p><strong>Date:</strong> {new Date(selectedNotification.expirationDate).toLocaleDateString('fr-FR')}</p>
                    <p><strong>Jours restants:</strong> 
                      <span className={`ml-1 font-medium ${
                        selectedNotification.daysRemaining < 0 ? 'text-red-600' :
                        selectedNotification.daysRemaining <= 7 ? 'text-orange-600' :
                        'text-blue-600'
                      }`}>
                        {selectedNotification.daysRemaining < 0 
                          ? `Expiré depuis ${Math.abs(selectedNotification.daysRemaining)} jour(s)`
                          : `${selectedNotification.daysRemaining} jour(s)`
                        }
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              
              {(selectedNotification.parcelNumber || selectedNotification.owner) && (
                <div>
                  <h4 className="font-medium text-gray-500 mb-2">Parcelle et Propriétaire</h4>
                  <div className="space-y-1 text-sm">
                    {selectedNotification.parcelNumber && (
                      <p><strong>Parcelle:</strong> {selectedNotification.parcelNumber}</p>
                    )}
                    {selectedNotification.owner && (
                      <p><strong>Propriétaire:</strong> {selectedNotification.owner}</p>
                    )}
                  </div>
                </div>
              )}
              
              <div>
                <h4 className="font-medium text-gray-500 mb-2">Message</h4>
                <div className="p-3 bg-gray-50 rounded text-sm">
                  {selectedNotification.message}
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4 border-t">
                {!selectedNotification.isRead && (
                  <Button 
                    variant="outline"
                    onClick={() => handleMarkAsRead(selectedNotification.id)}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Marquer comme lu
                  </Button>
                )}
                <Button 
                  variant="outline"
                  onClick={() => handleDeleteNotification(selectedNotification.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Supprimer
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
