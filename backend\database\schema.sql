-- GIS Land Management Database Schema
-- PostgreSQL with Post<PERSON><PERSON> extension

-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;

-- <PERSON>reate <PERSON><PERSON><PERSON> types
CREATE TYPE role_utilisateur AS ENUM ('ADMIN', 'JURIDIQUE', 'STANDARD');
CREATE TYPE type_document AS ENUM ('LOCATION', 'ACHAT', 'SERVITUDE', 'LITIGE');
CREATE TYPE statut_document AS ENUM ('ACTIF', 'EXPIRE', 'EN_RENOUVELLEMENT');

-- Users table
CREATE TABLE "Users" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nom VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role role_utilisateur DEFAULT 'STANDARD',
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Proprietaires table
CREATE TABLE "Proprietaires" (
    "idProprietaire" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nom VARCHAR(255) NOT NULL,
    cin VARCHAR(50) NOT NULL,
    contact VARCHAR(100),
    adresse TEXT,
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Parcelles table with PostGIS geometry
CREATE TABLE "Parcelles" (
    "idParcelle" VARCHAR(50) PRIMARY KEY,
    coordonnees GEOMETRY(POLYGON, 4326) NOT NULL,
    superficie FLOAT,
    statut statut_document DEFAULT 'ACTIF',
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Documents table
CREATE TABLE "Documents" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    numero VARCHAR(100) NOT NULL,
    type type_document NOT NULL,
    statut statut_document DEFAULT 'ACTIF',
    "dateSignature" DATE,
    echeance DATE,
    "fichierPath" VARCHAR(500),
    clauses TEXT,
    "parcelleId" VARCHAR(50) REFERENCES "Parcelles"("idParcelle"),
    "proprietaireId" UUID REFERENCES "Proprietaires"("idProprietaire"),
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- OCR Text table
CREATE TABLE "OCRTexts" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "documentId" UUID NOT NULL REFERENCES "Documents"(id) ON DELETE CASCADE,
    "extractedText" TEXT,
    confidence FLOAT,
    language VARCHAR(20) DEFAULT 'ara+fra',
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create spatial index for parcelles
CREATE INDEX idx_parcelles_coordonnees ON "Parcelles" USING GIST (coordonnees);

-- Create indexes for better performance
CREATE INDEX idx_documents_parcelleId ON "Documents"("parcelleId");
CREATE INDEX idx_documents_proprietaireId ON "Documents"("proprietaireId");
CREATE INDEX idx_documents_type ON "Documents"(type);
CREATE INDEX idx_documents_statut ON "Documents"(statut);
CREATE INDEX idx_ocrtexts_documentId ON "OCRTexts"("documentId");

-- Insert sample data
INSERT INTO "Users" (nom, email, password, role) VALUES
('Ahmed Alami', '<EMAIL>', '$2a$10$example_hashed_password', 'ADMIN'),
('Fatima Benali', '<EMAIL>', '$2a$10$example_hashed_password', 'JURIDIQUE'),
('Omar Tazi', '<EMAIL>', '$2a$10$example_hashed_password', 'STANDARD');

INSERT INTO "Proprietaires" ("idProprietaire", nom, cin, contact, adresse) VALUES
(gen_random_uuid(), 'Ahmed Benali', 'AB123456', '+************', 'Casablanca, Maroc'),
(gen_random_uuid(), 'Fatima Alaoui', 'FA789012', '+************', 'Rabat, Maroc'),
(gen_random_uuid(), 'Omar Tazi', 'OT345678', '+************', 'Marrakech, Maroc');

-- Insert sample parcels with geometry (example coordinates for Morocco)
INSERT INTO "Parcelles" ("idParcelle", coordonnees, superficie, statut) VALUES
('TF-123456', ST_GeomFromText('POLYGON((-7.5898 33.5731, -7.5888 33.5731, -7.5888 33.5741, -7.5898 33.5741, -7.5898 33.5731))', 4326), 2500.0, 'ACTIF'),
('TF-789012', ST_GeomFromText('POLYGON((-6.8416 34.0209, -6.8406 34.0209, -6.8406 34.0219, -6.8416 34.0219, -6.8416 34.0209))', 4326), 1800.0, 'ACTIF'),
('TF-345678', ST_GeomFromText('POLYGON((-7.9811 31.6295, -7.9801 31.6295, -7.9801 31.6305, -7.9811 31.6305, -7.9811 31.6295))', 4326), 3200.0, 'ACTIF');

-- Create views for easier querying
CREATE VIEW parcelles_with_documents AS
SELECT 
    p."idParcelle",
    p.superficie,
    p.statut as parcel_status,
    ST_AsGeoJSON(p.coordonnees) as geometry,
    COUNT(d.id) as document_count,
    ARRAY_AGG(d.type) as document_types
FROM "Parcelles" p
LEFT JOIN "Documents" d ON p."idParcelle" = d."parcelleId"
GROUP BY p."idParcelle", p.superficie, p.statut, p.coordonnees;

-- Function to get parcels within a bounding box
CREATE OR REPLACE FUNCTION get_parcels_in_bbox(
    min_lon FLOAT,
    min_lat FLOAT,
    max_lon FLOAT,
    max_lat FLOAT
)
RETURNS TABLE (
    parcel_id VARCHAR(50),
    geometry_json TEXT,
    superficie FLOAT,
    document_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p."idParcelle",
        ST_AsGeoJSON(p.coordonnees),
        p.superficie,
        COUNT(d.id)
    FROM "Parcelles" p
    LEFT JOIN "Documents" d ON p."idParcelle" = d."parcelleId"
    WHERE ST_Intersects(
        p.coordonnees,
        ST_MakeEnvelope(min_lon, min_lat, max_lon, max_lat, 4326)
    )
    GROUP BY p."idParcelle", p.coordonnees, p.superficie;
END;
$$ LANGUAGE plpgsql;
