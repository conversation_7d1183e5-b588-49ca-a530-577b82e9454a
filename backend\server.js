const express = require("express")
const cors = require("cors")
const jwt = require("jsonwebtoken")
const bcrypt = require("bcryptjs")
const multer = require("multer")
const path = require("path")
const fs = require("fs")
const Tesseract = require("tesseract.js")
const { Sequelize, DataTypes } = require("sequelize")
const swaggerJsdoc = require("swagger-jsdoc")
const swaggerUi = require("swagger-ui-express")

const app = express()
const PORT = process.env.PORT || 5000
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

// Database setup with PostgreSQL + PostGIS
const sequelize = new Sequelize(
  process.env.DB_NAME || "gis_land_db",
  process.env.DB_USER || "postgres",
  process.env.DB_PASSWORD || "password",
  {
    host: process.env.DB_HOST || "localhost",
    dialect: "postgres",
    logging: false,
  },
)

// Middleware
app.use(cors())
app.use(express.json())
app.use("/uploads", express.static("uploads"))

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = "uploads/documents"
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true })
    }
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9)
    cb(null, file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname))
  },
})

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf/
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase())
    const mimetype = allowedTypes.test(file.mimetype)

    if (mimetype && extname) {
      return cb(null, true)
    } else {
      cb(new Error("Only PDF, JPEG, JPG, and PNG files are allowed"))
    }
  },
})

// Models
const User = sequelize.define("User", {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  nom: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  role: {
    type: DataTypes.ENUM("ADMIN", "JURIDIQUE", "STANDARD"),
    defaultValue: "STANDARD",
  },
})

const Proprietaire = sequelize.define("Proprietaire", {
  idProprietaire: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  nom: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  cin: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  contact: {
    type: DataTypes.STRING,
  },
  adresse: {
    type: DataTypes.TEXT,
  },
})

const Parcelle = sequelize.define("Parcelle", {
  idParcelle: {
    type: DataTypes.STRING,
    primaryKey: true,
  },
  coordonnees: {
    type: DataTypes.GEOMETRY("POLYGON"),
    allowNull: false,
  },
  superficie: {
    type: DataTypes.FLOAT,
  },
  statut: {
    type: DataTypes.ENUM("ACTIF", "EXPIRE", "EN_RENOUVELLEMENT"),
    defaultValue: "ACTIF",
  },
})

const Document = sequelize.define("Document", {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  numero: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  type: {
    type: DataTypes.ENUM("LOCATION", "ACHAT", "SERVITUDE", "LITIGE"),
    allowNull: false,
  },
  statut: {
    type: DataTypes.ENUM("ACTIF", "EXPIRE", "EN_RENOUVELLEMENT"),
    defaultValue: "ACTIF",
  },
  dateSignature: {
    type: DataTypes.DATE,
  },
  echeance: {
    type: DataTypes.DATE,
  },
  fichierPath: {
    type: DataTypes.STRING,
  },
  clauses: {
    type: DataTypes.TEXT,
  },
})

const OCRText = sequelize.define("OCRText", {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  documentId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  extractedText: {
    type: DataTypes.TEXT,
  },
  confidence: {
    type: DataTypes.FLOAT,
  },
  language: {
    type: DataTypes.STRING,
    defaultValue: "ara+fra",
  },
})

// Associations
Proprietaire.hasMany(Document, { foreignKey: "proprietaireId" })
Document.belongsTo(Proprietaire, { foreignKey: "proprietaireId" })

Parcelle.hasMany(Document, { foreignKey: "parcelleId" })
Document.belongsTo(Parcelle, { foreignKey: "parcelleId" })

Document.hasOne(OCRText, { foreignKey: "documentId" })
OCRText.belongsTo(Document, { foreignKey: "documentId" })

// Middleware for authentication
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers["authorization"]
  const token = authHeader && authHeader.split(" ")[1]

  if (!token) {
    return res.status(401).json({ error: "Access token required" })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: "Invalid token" })
    }
    req.user = user
    next()
  })
}

// Role-based middleware
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: "Insufficient permissions" })
    }
    next()
  }
}

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "GIS Land Management API",
      version: "1.0.0",
      description: "API for managing land parcels and legal contracts",
    },
    servers: [
      {
        url: `http://localhost:${PORT}`,
        description: "Development server",
      },
    ],
  },
  apis: ["./server.js"], // Path to the API docs
}

const specs = swaggerJsdoc(swaggerOptions)
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(specs))

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: User login
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
app.post("/api/auth/login", async (req, res) => {
  try {
    const { email, password } = req.body

    const user = await User.findOne({ where: { email } })
    if (!user) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const token = jwt.sign({ id: user.id, email: user.email, role: user.role }, JWT_SECRET, { expiresIn: "24h" })

    res.json({
      token,
      user: {
        id: user.id,
        nom: user.nom,
        email: user.email,
        role: user.role,
      },
    })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register new user (Admin only)
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               nom:
 *                 type: string
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: [ADMIN, JURIDIQUE, STANDARD]
 *     responses:
 *       201:
 *         description: User created successfully
 */
app.post("/api/auth/register", authenticateToken, requireRole(["ADMIN"]), async (req, res) => {
  try {
    const { nom, email, password, role } = req.body

    const existingUser = await User.findOne({ where: { email } })
    if (existingUser) {
      return res.status(400).json({ error: "User already exists" })
    }

    const hashedPassword = await bcrypt.hash(password, 10)

    const user = await User.create({
      nom,
      email,
      password: hashedPassword,
      role: role || "STANDARD",
    })

    res.status(201).json({
      message: "User created successfully",
      user: {
        id: user.id,
        nom: user.nom,
        email: user.email,
        role: user.role,
      },
    })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

/**
 * @swagger
 * /api/parcels:
 *   get:
 *     summary: Get all parcels
 *     tags: [Parcels]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of parcels
 */
app.get("/api/parcels", authenticateToken, async (req, res) => {
  try {
    const parcels = await Parcelle.findAll({
      include: [
        {
          model: Document,
          include: [Proprietaire],
        },
      ],
    })
    res.json(parcels)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

/**
 * @swagger
 * /api/parcels/{id}/documents:
 *   get:
 *     summary: Get documents for a specific parcel
 *     tags: [Parcels]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of documents for the parcel
 */
app.get("/api/parcels/:id/documents", authenticateToken, async (req, res) => {
  try {
    const { id } = req.params
    const documents = await Document.findAll({
      where: { parcelleId: id },
      include: [Proprietaire, OCRText],
    })
    res.json(documents)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

/**
 * @swagger
 * /api/documents/upload:
 *   post:
 *     summary: Upload document with OCR processing
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               parcelleId:
 *                 type: string
 *               type:
 *                 type: string
 *               proprietaireId:
 *                 type: string
 *     responses:
 *       201:
 *         description: Document uploaded and processed successfully
 */
app.post(
  "/api/documents/upload",
  authenticateToken,
  requireRole(["ADMIN", "JURIDIQUE"]),
  upload.single("file"),
  async (req, res) => {
    try {
      const { parcelleId, type, proprietaireId } = req.body
      const file = req.file

      if (!file) {
        return res.status(400).json({ error: "No file uploaded" })
      }

      // Create document record
      const document = await Document.create({
        numero: `DOC-${Date.now()}`,
        type,
        fichierPath: file.path,
        parcelleId,
        proprietaireId,
        dateSignature: new Date(),
      })

      // Process OCR for images and PDFs
      let ocrResult = null
      if (file.mimetype.startsWith("image/")) {
        try {
          const {
            data: { text, confidence },
          } = await Tesseract.recognize(
            file.path,
            "ara+fra", // Arabic + French
            {
              logger: (m) => console.log(m),
            },
          )

          // Save OCR text to file
          const txtFilePath = file.path.replace(path.extname(file.path), ".txt")
          fs.writeFileSync(txtFilePath, text, "utf8")

          // Save OCR result to database
          ocrResult = await OCRText.create({
            documentId: document.id,
            extractedText: text,
            confidence: confidence,
            language: "ara+fra",
          })
        } catch (ocrError) {
          console.error("OCR processing failed:", ocrError)
        }
      }

      res.status(201).json({
        message: "Document uploaded successfully",
        document: {
          id: document.id,
          numero: document.numero,
          type: document.type,
          fichierPath: document.fichierPath,
          ocrProcessed: !!ocrResult,
        },
      })
    } catch (error) {
      res.status(500).json({ error: error.message })
    }
  },
)

/**
 * @swagger
 * /api/contracts:
 *   get:
 *     summary: Get all contracts
 *     tags: [Contracts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of contracts
 */
app.get("/api/contracts", authenticateToken, async (req, res) => {
  try {
    const contracts = await Document.findAll({
      include: [Proprietaire, Parcelle, OCRText],
    })
    res.json(contracts)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

/**
 * @swagger
 * /api/contracts:
 *   post:
 *     summary: Create new contract
 *     tags: [Contracts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               numero:
 *                 type: string
 *               type:
 *                 type: string
 *               parcelleId:
 *                 type: string
 *               proprietaireId:
 *                 type: string
 *               dateSignature:
 *                 type: string
 *               echeance:
 *                 type: string
 *               clauses:
 *                 type: string
 *     responses:
 *       201:
 *         description: Contract created successfully
 */
app.post("/api/contracts", authenticateToken, requireRole(["ADMIN", "JURIDIQUE"]), async (req, res) => {
  try {
    const contract = await Document.create(req.body)
    res.status(201).json(contract)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

/**
 * @swagger
 * /api/users:
 *   get:
 *     summary: Get all users (Admin only)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of users
 */
app.get("/api/users", authenticateToken, requireRole(["ADMIN"]), async (req, res) => {
  try {
    const users = await User.findAll({
      attributes: { exclude: ["password"] },
    })
    res.json(users)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === "LIMIT_FILE_SIZE") {
      return res.status(400).json({ error: "File too large" })
    }
  }
  res.status(500).json({ error: error.message })
})

// Initialize database and start server
async function startServer() {
  try {
    await sequelize.authenticate()
    console.log("Database connection established successfully.")

    await sequelize.sync({ force: false })
    console.log("Database synchronized.")

    // Create default admin user if not exists
    const adminExists = await User.findOne({ where: { email: "<EMAIL>" } })
    if (!adminExists) {
      const hashedPassword = await bcrypt.hash("admin123", 10)
      await User.create({
        nom: "Adam SMIDA",
        email: "<EMAIL>",
        password: hashedPassword,
        role: "ADMIN",
      })
      console.log("Default admin user created.")
    }

    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`)
      console.log(`API Documentation available at http://localhost:${PORT}/api-docs`)
    })
  } catch (error) {
    console.error("Unable to start server:", error)
  }
}

startServer()
