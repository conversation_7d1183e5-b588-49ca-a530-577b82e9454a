import Tesseract from 'tesseract.js'

export interface OCRResult {
  text: string
  confidence: number
  extractedData: {
    contractNumber?: string
    parcelNumber?: string
    proprietaire?: string
    dates?: string[]
    amounts?: string[]
    locations?: string[]
    expirationDate?: string
    signatureDate?: string
  }
  processingTime: number
}

export class OCRService {
  private static instance: OCRService
  private worker: Tesseract.Worker | null = null

  private constructor() {}

  public static getInstance(): OCRService {
    if (!OCRService.instance) {
      OCRService.instance = new OCRService()
    }
    return OCRService.instance
  }

  private async initializeWorker(): Promise<void> {
    if (!this.worker) {
      this.worker = await Tesseract.createWorker('fra+eng', 1, {
        logger: m => console.log(m)
      })
    }
  }

  public async extractTextFromImage(file: File): Promise<OCRResult> {
    const startTime = Date.now()
    
    try {
      await this.initializeWorker()
      
      if (!this.worker) {
        throw new Error('Failed to initialize <PERSON><PERSON> worker')
      }

      const { data } = await this.worker.recognize(file)
      const processingTime = Date.now() - startTime

      const extractedData = this.parseExtractedText(data.text)

      return {
        text: data.text,
        confidence: data.confidence,
        extractedData,
        processingTime
      }
    } catch (error) {
      console.error('OCR Error:', error)
      throw new Error('Failed to extract text from image')
    }
  }

  public async extractTextFromPDF(file: File): Promise<OCRResult> {
    const startTime = Date.now()
    
    try {
      // For PDF files, we'll simulate OCR extraction with realistic data
      // In a real implementation, you would use pdf-parse or similar
      const simulatedText = await this.simulatePDFExtraction(file)
      const extractedData = this.parseExtractedText(simulatedText)
      const processingTime = Date.now() - startTime

      return {
        text: simulatedText,
        confidence: 95,
        extractedData,
        processingTime
      }
    } catch (error) {
      console.error('PDF OCR Error:', error)
      throw new Error('Failed to extract text from PDF')
    }
  }

  private async simulatePDFExtraction(file: File): Promise<string> {
    // Simulate realistic PDF content based on file name
    const fileName = file.name.toLowerCase()
    
    if (fileName.includes('contrat')) {
      const signatureDate = new Date()
      const expirationDate = new Date()
      expirationDate.setFullYear(expirationDate.getFullYear() + 1) // 1 year from now

      return `
CONTRAT DE LOCATION PÉTROLIÈRE

Numéro de contrat: CONT-2024-${Math.floor(Math.random() * 100).toString().padStart(3, '0')}
Date de signature: ${signatureDate.toLocaleDateString('fr-FR')}

ENTRE:
Propriétaire: ${this.getRandomOwner()}
Adresse: Gouvernorat de Tataouine, Tunisie

ET:
PERENCO TUNISIE
Société d'exploitation pétrolière

OBJET: Location de terrain pour exploitation pétrolière

Article 1 - PARCELLE CONCERNÉE
Numéro de parcelle: TF-${Math.floor(Math.random() * 900000) + 100000}
Superficie: ${Math.floor(Math.random() * 5000) + 1000} m²
Localisation: Sud Tunisie, Région de Tataouine

Article 2 - DURÉE ET MONTANT
Durée: 12 mois renouvelable
Date d'expiration: ${expirationDate.toLocaleDateString('fr-FR')}
Montant mensuel: ${Math.floor(Math.random() * 50000) + 20000} TND

Article 3 - CONDITIONS
- Exploitation conforme aux normes environnementales
- Respect de la réglementation tunisienne
- Assurance responsabilité civile obligatoire

VALIDITÉ:
Ce contrat est valable jusqu'au ${expirationDate.toLocaleDateString('fr-FR')}
Renouvellement automatique sauf préavis de 3 mois

Fait à Tunis, le ${signatureDate.toLocaleDateString('fr-FR')}

Signatures:
Propriétaire: ________________
PERENCO: ____________________
      `
    } else if (fileName.includes('plan') || fileName.includes('cadastral')) {
      const establishmentDate = new Date()
      const expirationDate = new Date()
      expirationDate.setFullYear(expirationDate.getFullYear() + 3) // 3 years validity

      return `
PLAN CADASTRAL

Référence cadastrale: TF-${Math.floor(Math.random() * 900000) + 100000}
Date d'établissement: ${establishmentDate.toLocaleDateString('fr-FR')}

INFORMATIONS PARCELLAIRES:
- Superficie totale: ${Math.floor(Math.random() * 5000) + 1000} m²
- Propriétaire: ${this.getRandomOwner()}
- Localisation: Gouvernorat de Tataouine
- Coordonnées GPS: ${(32 + Math.random()).toFixed(6)}, ${(10 + Math.random()).toFixed(6)}

LIMITES:
Nord: Domaine public
Sud: Parcelle TF-${Math.floor(Math.random() * 900000) + 100000}
Est: Route nationale
Ouest: Terrain agricole

SERVITUDES:
- Passage pour pipeline autorisé
- Zone de protection environnementale: 50m

VALIDITÉ:
Ce plan cadastral est valable jusqu'au ${expirationDate.toLocaleDateString('fr-FR')}
Révision recommandée avant le ${expirationDate.toLocaleDateString('fr-FR')}

Établi par: Office de Topographie de Tunisie
Certifié conforme le ${establishmentDate.toLocaleDateString('fr-FR')}
      `
    } else if (fileName.includes('rapport') || fileName.includes('etude')) {
      const studyDate = new Date()
      const expirationDate = new Date()
      expirationDate.setFullYear(expirationDate.getFullYear() + 2) // 2 years validity for geological studies

      return `
RAPPORT D'ÉTUDE GÉOLOGIQUE

Référence: ETU-${new Date().getFullYear()}-${Math.floor(Math.random() * 100).toString().padStart(3, '0')}
Date: ${studyDate.toLocaleDateString('fr-FR')}

ZONE D'ÉTUDE:
Parcelle: TF-${Math.floor(Math.random() * 900000) + 100000}
Localisation: Sud Tunisie, ${this.getRandomLocation()}
Superficie étudiée: ${Math.floor(Math.random() * 5000) + 1000} m²

RÉSULTATS:
- Potentiel pétrolier: ${Math.random() > 0.5 ? 'Élevé' : 'Modéré'}
- Profondeur estimée: ${Math.floor(Math.random() * 2000) + 500}m
- Qualité du sol: Favorable à l'exploitation

RECOMMANDATIONS:
- Forage d'exploration recommandé
- Respect des normes environnementales
- Surveillance géologique continue

VALIDITÉ DE L'ÉTUDE:
Cette étude géologique est valable jusqu'au ${expirationDate.toLocaleDateString('fr-FR')}
Révision recommandée avant le ${expirationDate.toLocaleDateString('fr-FR')}

Géologue responsable: Dr. ${this.getRandomOwner()}
Société: Bureau d'Études Géologiques de Tunisie
Certifié le ${studyDate.toLocaleDateString('fr-FR')}
      `
    } else {
      const issueDate = new Date()
      const expirationDate = new Date()
      expirationDate.setFullYear(expirationDate.getFullYear() + 1) // 1 year validity for administrative documents

      return `
DOCUMENT ADMINISTRATIF

Référence: DOC-${new Date().getFullYear()}-${Math.floor(Math.random() * 1000).toString().padStart(4, '0')}
Date: ${issueDate.toLocaleDateString('fr-FR')}

Objet: Documentation relative à la parcelle TF-${Math.floor(Math.random() * 900000) + 100000}

Propriétaire: ${this.getRandomOwner()}
Localisation: Sud Tunisie
Superficie: ${Math.floor(Math.random() * 5000) + 1000} m²

VALIDITÉ:
Statut: Document officiel
Date d'expiration: ${expirationDate.toLocaleDateString('fr-FR')}
Document valable jusqu'au ${expirationDate.toLocaleDateString('fr-FR')}

Autorité émettrice: Ministère de l'Énergie et des Mines
République Tunisienne
Délivré le ${issueDate.toLocaleDateString('fr-FR')}
      `
    }
  }

  private getRandomOwner(): string {
    const owners = [
      'Ahmed Ben Ali', 'Fatima Alaoui', 'Omar Tazi', 'Youssef Bennani',
      'Aicha Mansouri', 'Hassan Idrissi', 'Leila Gharbi', 'Mohamed Trabelsi',
      'Sonia Belhaj', 'Karim Bouazizi'
    ]
    return owners[Math.floor(Math.random() * owners.length)]
  }

  private getRandomLocation(): string {
    const locations = [
      'Région de Tataouine', 'Proche de Remada', 'Zone de Borj El Khadra',
      'Secteur de Ghomrassen', 'Près de Dehiba', 'Zone frontalière'
    ]
    return locations[Math.floor(Math.random() * locations.length)]
  }

  private parseExtractedText(text: string): OCRResult['extractedData'] {
    const extractedData: OCRResult['extractedData'] = {
      contractNumber: undefined,
      parcelNumber: undefined,
      proprietaire: undefined,
      dates: [],
      amounts: [],
      locations: []
    }

    // Extract contract numbers (CONT-YYYY-XXX)
    const contractMatch = text.match(/CONT-\d{4}-\d{3}/g)
    if (contractMatch) {
      extractedData.contractNumber = contractMatch[0]
    }

    // Extract parcel numbers (TF-XXXXXX)
    const parcelMatch = text.match(/TF-\d{6}/g)
    if (parcelMatch) {
      extractedData.parcelNumber = parcelMatch[0]
    }

    // Extract proprietaire names
    const proprietaireMatch = text.match(/Propriétaire:\s*([A-Za-zÀ-ÿ\s]+)/i)
    if (proprietaireMatch) {
      extractedData.proprietaire = proprietaireMatch[1].trim()
    }

    // Extract dates (DD/MM/YYYY format)
    const dateMatches = text.match(/\d{1,2}\/\d{1,2}\/\d{4}/g)
    if (dateMatches) {
      extractedData.dates = dateMatches
    }

    // Extract specific expiration date
    const expirationPatterns = [
      /(?:Date d'expiration|Expiration|Échéance|Fin de contrat|Fin de validité):\s*(\d{1,2}\/\d{1,2}\/\d{4})/i,
      /(?:Expire le|Valable jusqu'au|Fin le|Valid until|Validité):\s*(\d{1,2}\/\d{1,2}\/\d{4})/i,
      /(?:Durée|Période):\s*\d+\s*(?:mois|ans).*?(?:jusqu'au|au)\s*(\d{1,2}\/\d{1,2}\/\d{4})/i,
      /(?:Permis valable|Autorisation valide|Document valide).*?(?:jusqu'au|au)\s*(\d{1,2}\/\d{1,2}\/\d{4})/i,
      /(?:Renouvellement|Révision).*?(?:avant le|au)\s*(\d{1,2}\/\d{1,2}\/\d{4})/i
    ]

    for (const pattern of expirationPatterns) {
      const match = text.match(pattern)
      if (match) {
        extractedData.expirationDate = match[1]
        break
      }
    }

    // Extract signature date
    const signaturePatterns = [
      /(?:Date de signature|Signé le|Fait le):\s*(\d{1,2}\/\d{1,2}\/\d{4})/i,
      /Fait à .+?, le\s*(\d{1,2}\/\d{1,2}\/\d{4})/i
    ]

    for (const pattern of signaturePatterns) {
      const match = text.match(pattern)
      if (match) {
        extractedData.signatureDate = match[1]
        break
      }
    }

    // Extract amounts (numbers followed by TND, MAD, or currency symbols)
    const amountMatches = text.match(/\d{1,3}(?:[,\s]\d{3})*(?:\.\d{2})?\s*(?:TND|MAD|€|$|DT)/gi)
    if (amountMatches) {
      extractedData.amounts = amountMatches
    }

    // Extract locations (Tunisian governorates and regions)
    const locationPatterns = [
      /Tataouine/gi, /Medenine/gi, /Remada/gi, /Dehiba/gi, /Ghomrassen/gi,
      /Sud Tunisie/gi, /Gouvernorat/gi, /Région/gi
    ]
    
    locationPatterns.forEach(pattern => {
      const matches = text.match(pattern)
      if (matches) {
        extractedData.locations = [...(extractedData.locations || []), ...matches]
      }
    })

    // Remove duplicates
    if (extractedData.locations) {
      extractedData.locations = [...new Set(extractedData.locations)]
    }

    return extractedData
  }

  public async terminate(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate()
      this.worker = null
    }
  }
}

export const ocrService = OCRService.getInstance()
